<!-- The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work. -->
<template>
    <!-- OAuth回调处理中的加载界面 -->
    <div v-show="shouldShowProcessing" class="processing-interface min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100" style="display: none;">
        <div class="max-w-md w-full mx-4">
            <div class="bg-white rounded-2xl shadow-xl p-8 text-center animate-fadeIn">
                <!-- 平台标题 -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">AI测试集成平台</h1>
                    <p class="text-gray-600">正在处理认证信息...</p>
                </div>

                <!-- 加载动画 -->
                <div class="mb-8">
                    <div class="w-48 h-48 mx-auto mb-6 flex items-center justify-center">
                        <div class="text-center">
                            <loading-outlined class="text-6xl text-blue-500 animate-spin mb-4" />
                            <p class="text-lg text-gray-700 font-medium">认证成功</p>
                            <p class="text-sm text-gray-500 mt-2">正在跳转到主页面...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 简洁的统一认证扫码登录界面 -->
    <div v-show="!shouldShowProcessing" class="login-interface min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div class="max-w-md w-full mx-4">
            <!-- 登录卡片 -->
            <div class="bg-white rounded-2xl shadow-xl p-8 text-center animate-fadeIn">
                <!-- 平台标题 -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">AI测试集成平台</h1>
                    <p class="text-gray-600">智能化测试解决方案</p>
                </div>

                <!-- 扫码登录区域 -->
                <div class="mb-8">
                    <!-- 二维码占位区域 -->
                    <div class="w-48 h-48 mx-auto mb-6 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <p class="text-sm text-gray-500">扫码登录</p>
                        </div>
                    </div>

                    <!-- 登录按钮 -->
                    <button
                        @click="handleOAuthLogin"
                        :disabled="isOAuthLoading"
                        class="w-full py-4 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 hover:shadow-xl"
                        :class="{ 'opacity-75 cursor-not-allowed': isOAuthLoading }">
                        <span v-if="isOAuthLoading" class="flex items-center justify-center">
                            <loading-outlined class="animate-spin mr-2" />
                            跳转认证中...
                        </span>
                        <span v-else class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            TAM统一认证登录
                        </span>
                    </button>
                </div>

                <!-- 登录说明 -->
                <div class="text-left">
                    <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                            </svg>
                            <div class="text-sm text-blue-700 flex-1">
                                <p class="font-medium mb-2">登录说明：</p>
                                <ul class="space-y-1.5">
                                    <li class="flex items-start">
                                        <span class="inline-block w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        <span class="leading-relaxed">点击按钮跳转到TAM认证中心</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="inline-block w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        <span class="leading-relaxed">使用公司账号密码登录</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="inline-block w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        <span class="leading-relaxed">认证成功后自动返回</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 注释掉的原始复杂界面 -->
    <!--
    <div class="min-h-screen flex items-center justify-center bg-white">
        <div class="container mx-auto flex flex-col md:flex-row w-full max-w-6xl shadow-lg rounded-lg overflow-hidden animate-fadeIn">
            <div class="w-full md:w-1/2 flex flex-col justify-center items-center p-12 relative overflow-hidden">
                <div class="relative z-10">
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 tracking-wide mb-6 text-center md:text-left">
                        AI测试集成平台
                    </h1>
                    <p class="text-lg text-gray-700 max-w-md">
                        智能化测试解决方案，让您的测试流程更加简单高效
                    </p>
                </div>
                <div class="absolute inset-0 z-0 bg-gradient-animation">
                    <div class="floating-particles"></div>
                    <img :src="backgroundImageUrl" alt="AI测试平台背景"
                        class="w-full h-full object-cover object-center opacity-10" />
                </div>
            </div>
            <div class="w-full md:w-1/2 bg-white p-12 flex flex-col justify-center border-l border-gray-200">
                <h2 class="text-2xl font-semibold text-gray-800 mb-8">欢迎登录</h2>
                <div class="space-y-6">
                    <div class="text-center">
                        <p class="text-gray-600 mb-6">使用公司统一认证登录</p>
                        <button @click="handleOAuthLogin" :disabled="isOAuthLoading"
                            class="w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md shadow-sm transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 !rounded-button whitespace-nowrap cursor-pointer"
                            :class="{ 'opacity-75 cursor-not-allowed': isOAuthLoading }">
                            <span v-if="isOAuthLoading" class="flex items-center justify-center">
                                <loading-outlined class="animate-spin mr-2" />
                                跳转认证中...
                            </span>
                            <span v-else class="flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                TAM统一认证登录
                            </span>
                        </button>
                    </div>
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-blue-400 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                            </svg>
                            <div class="text-sm text-blue-700 flex-1">
                                <p class="font-medium mb-2">统一认证说明：</p>
                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <span class="inline-block w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        <span class="leading-relaxed">点击按钮将跳转到TAM零信任身份服务中心</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="inline-block w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        <span class="leading-relaxed">使用您的公司账号和密码进行认证</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="inline-block w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        <span class="leading-relaxed">认证成功后将自动返回并完成登录</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="inline-block w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                        <span class="leading-relaxed">支持多因子认证和单点登录</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    -->
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import { LoadingOutlined } from '@ant-design/icons-vue';
// 状态变量 - 注释掉不再使用的账号密码登录相关变量
// const username = ref('');
// const password = ref('');
// const rememberMe = ref(false);
// const isLoading = ref(false);
// const usernameError = ref('');
// const passwordError = ref('');

// OAuth认证相关状态
// const authType = ref('password'); // 'password' | 'oauth' - 不再需要选择认证方式
const isOAuthLoading = ref(false);

// 立即检查URL参数，如果有OAuth回调参数，立即设置处理状态
const urlParams = new URLSearchParams(window.location.search);
const hasOAuthCallback = urlParams.has('code') || urlParams.has('error') ||
                        !!localStorage.getItem('oauth_callback_pending');
const isProcessingOAuth = ref(hasOAuthCallback); // 是否正在处理OAuth回调

// 如果检测到OAuth回调，立即打印日志并设置页面状态
if (hasOAuthCallback) {
    console.log('🚀 页面加载时检测到OAuth回调参数，立即设置处理状态');

    // 立即添加CSS样式来隐藏登录界面，显示处理界面
    const style = document.createElement('style');
    style.textContent = `
        .login-interface { display: none !important; }
        .processing-interface { display: flex !important; }
    `;
    document.head.appendChild(style);
}

// 计算属性：检查URL中是否有OAuth回调参数
const hasOAuthCallbackInUrl = computed(() => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.has('code') || urlParams.has('error');
});

// 计算属性：是否应该显示处理中界面
const shouldShowProcessing = computed(() => {
    return hasOAuthCallbackInUrl.value || isProcessingOAuth.value;
});


// 背景图片变量已删除，新界面不再需要
// 登录处理
import router from '../router/index';
import { oauthCompleteLogin } from '../utils/api'; // 只保留OAuth登录相关的API
// 添加页面载入动画
onMounted(() => {
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transition = 'opacity 0.5s ease-in-out';
    }, 100);

    // 记住我逻辑优化 - 注释掉，因为只使用OAuth登录
    // const rememberMeStr = localStorage.getItem('rememberMe');
    // if (rememberMeStr === 'true') {
    //     rememberMe.value = true;
    //     const userInfoStr = localStorage.getItem('userInfo');
    //     if (userInfoStr) {
    //         try {
    //             const userInfo = JSON.parse(userInfoStr);
    //             if (userInfo && userInfo.username) {
    //                 username.value = userInfo.username;
    //             }
    //         } catch (e) {
    //             // ignore parse error
    //         }
    //     }
    // } else {
    //     rememberMe.value = false;
    //     username.value = '';
    // }

    // 立即检查OAuth回调
    console.log('🔍 页面加载，检查OAuth回调...');
    console.log('当前URL:', window.location.href);

    // 无论如何都检查OAuth回调
    checkOAuthCallback();

    // 额外检查：如果URL中有OAuth参数，再次确保处理
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('code') || urlParams.has('error')) {
        console.log('🎯 发现OAuth回调参数，确保处理...');
        setTimeout(() => {
            checkOAuthCallback();
        }, 500);
    }


});

// 处理OAuth登录
const handleOAuthLogin = () => {
    isOAuthLoading.value = true;

    try {
        // 生成state用于安全验证
        const state = Date.now().toString();

        // 保存state到localStorage用于回调验证
        localStorage.setItem('oauth_auth_state', state);

        // OAuth配置
        const oauthConfig = {
            client_id: 'UK59v8',
            auth_base_url: 'https://sso.das-security.cn',
            authorization_endpoint: '/iam/auth/oauth2/sso',
            redirect_uri: 'http://10.113.7.86:8080/login'
        };

        // 生成nonce
        const nonce = Date.now().toString() + Math.random().toString(36).substring(2, 11);

        // 构建认证URL
        const authUrl = `${oauthConfig.auth_base_url}${oauthConfig.authorization_endpoint}?` +
            `redirect_uri=${encodeURIComponent(oauthConfig.redirect_uri)}&` +
            `client_id=${oauthConfig.client_id}&` +
            `state=${state}&` +
            `response_type=code&` +
            `nonce=${nonce}`;

        console.log('跳转到OAuth认证页面:', authUrl);

        // 跳转到OAuth认证页面
        window.location.href = authUrl;
    } catch (error) {
        isOAuthLoading.value = false;
        message.error('跳转认证失败，请稍后重试');
        console.error('OAuth认证跳转失败:', error);
    }
};

// OAuth认证回调处理
const checkOAuthCallback = async () => {
    console.log('=== 开始检查OAuth回调 ===');

    // 首先检查localStorage中是否有路由守卫保存的OAuth参数
    const hasPendingCallback = localStorage.getItem('oauth_callback_pending');
    let code, state, error;

    if (hasPendingCallback) {
        console.log('🎯 发现路由守卫保存的OAuth回调参数');
        // 从localStorage获取参数
        code = localStorage.getItem('oauth_callback_code');
        state = localStorage.getItem('oauth_callback_state');
        error = localStorage.getItem('oauth_callback_error');

        // 清理localStorage中的回调参数
        localStorage.removeItem('oauth_callback_pending');
        localStorage.removeItem('oauth_callback_code');
        localStorage.removeItem('oauth_callback_state');
        localStorage.removeItem('oauth_callback_error');
        localStorage.removeItem('oauth_callback_error_description');

        console.log('从localStorage获取的参数:');
        console.log('- code:', code ? `存在(${code.length}字符)` : '不存在');
        console.log('- state:', state || '不存在');
        console.log('- error:', error || '不存在');
    } else {
        // 兼容直接访问的情况，从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        code = urlParams.get('code');
        state = urlParams.get('state');
        error = urlParams.get('error');

        console.log('从URL参数获取的参数:');
        console.log('- code:', code ? `存在(${code.length}字符)` : '不存在');
        console.log('- state:', state || '不存在');
        console.log('- error:', error || '不存在');
    }

    if (error) {
        console.error('OAuth认证错误:', error);
        message.error(`认证失败: ${error}`);
        return;
    }

    if (code && state) {
        console.log('🚀 检测到OAuth回调参数，立即调用后端接口！');
        console.log('Code:', code.substring(0, 20) + '...');
        console.log('State:', state);

        try {
            // 设置处理OAuth回调状态，隐藏登录界面
            isProcessingOAuth.value = true;
            isOAuthLoading.value = true;

            console.log('📞 调用后端oauth-complete-login接口...');
            const result = await oauthCompleteLogin({ code, state });

            console.log('✅ 后端接口调用成功！结果:', result);

            // 立即清除URL中的OAuth参数，防止重复处理
            window.history.replaceState({}, document.title, window.location.pathname);
            console.log('URL参数已清除');

            handleLoginSuccess({ code: 200, data: result });

        } catch (error: any) {
            console.error('❌ 后端接口调用失败:', error);
            isProcessingOAuth.value = false;
            isOAuthLoading.value = false;

            // 清除URL参数，即使失败也要清除
            window.history.replaceState({}, document.title, window.location.pathname);

            message.error('OAuth登录失败: ' + (error?.message || '未知错误'));
        }
    } else {
        console.log('ℹ️ 未发现OAuth回调参数');
    }
};



// 统一处理登录成功逻辑
const handleLoginSuccess = (data: any) => {
    console.log('=== 开始处理登录成功逻辑 ===');

    // isLoading.value = false; // 注释掉，因为不再使用账号密码登录
    isOAuthLoading.value = false;

    // 兼容不同的响应格式
    const responseData = data.data || data;
    const token = responseData.token;
    const user = responseData.user;

    console.log('登录响应数据:', { token: token ? '存在' : '不存在', user: user ? '存在' : '不存在' });

    // 确保token和user都存在
    if (!token || !user) {
        console.error('登录响应数据不完整:', { token, user });
        message.error('登录响应数据不完整，请重试');
        return;
    }

    try {
        // 先存储token
        localStorage.setItem('token', token);
        console.log('Token已存储');

        // 再存储用户信息
        localStorage.setItem('userInfo', JSON.stringify(user));
        console.log('用户信息已存储');

        // 将指定字段单独存储到 localStorage
        localStorage.setItem('userId', user.userId || '');
        localStorage.setItem('realName', user.realName || '');
        localStorage.setItem('userType', user.userType || '');
        localStorage.setItem('username', user.username || '');
        localStorage.setItem('role', user.role || '');
        localStorage.setItem('lastLogin', user.lastLogin || Date.now().toString());
        localStorage.setItem('product', user.product || '');
        console.log('用户详细信息已存储');

        // 清除OAuth回调相关的参数，防止重复处理
        localStorage.removeItem('oauth_callback_pending');
        localStorage.removeItem('oauth_callback_code');
        localStorage.removeItem('oauth_callback_state');
        localStorage.removeItem('oauth_callback_error');
        localStorage.removeItem('oauth_callback_error_description');
        console.log('OAuth回调参数已清除');

        // 验证数据是否正确存储
        const storedToken = localStorage.getItem('token');
        const storedUserInfo = localStorage.getItem('userInfo');

        if (storedToken && storedUserInfo) {
            console.log('数据存储验证成功，准备跳转');
            message.success('登录成功');

            // 使用setTimeout确保所有数据都已经存储完成
            setTimeout(() => {
                console.log('跳转到主页');
                router.push('/');
            }, 100);
        } else {
            console.error('数据存储验证失败');
            message.error('数据存储失败，请重试');
        }

    } catch (error) {
        console.error('存储用户数据时出错:', error);
        message.error('存储用户数据失败，请重试');
    }
};

// 注释掉账号密码登录处理函数
// const handleLogin = () => {
//     if (!username.value || !password.value) {
//         message.error('请输入用户名和密码');
//         return;
//     }
//     isLoading.value = true;
//     // 调用真实登录接口
//     login(username.value, password.value, rememberMe.value)
//       .then((res) => {
//         // 兼容axios返回结构
//         const data = res.data ? res.data : res;
//         handleLoginSuccess(data);

//         if (rememberMe.value) {
//           console.log('记住密码已启用');
//         }
//       })
//       .catch((err) => {
//         isLoading.value = false;
//         message.error(err.message || '登录失败');
//       });
// };
</script>
<style scoped>
/* 背景动画 */
.bg-gradient-animation {
    background: linear-gradient(45deg, #f3f4f6, #e5e7eb, #f3f4f6);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at center, #3b82f6 1px, transparent 1px);
    background-size: 50px 50px;
    animation: particleFloat 20s linear infinite;
    opacity: 0.3;
}

.floating-particles::after {
    background-size: 30px 30px;
    animation-duration: 15s;
    animation-delay: -5s;
}

@keyframes gradientBG {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(0) translateX(0);
    }

    25% {
        transform: translateY(-20px) translateX(10px);
    }

    50% {
        transform: translateY(-40px) translateX(0);
    }

    75% {
        transform: translateY(-20px) translateX(-10px);
    }

    100% {
        transform: translateY(0) translateX(0);
    }
}

/* 输入框聚焦动画 */
:deep(.ant-input-affix-wrapper),
:deep(.ant-input) {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

:deep(.ant-input-affix-wrapper:focus-within) {
    transform: scale(1.02);
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.15);
}

/* 按钮动画 */
button[type="submit"] {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

button[type="submit"]::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease-out, height 0.6s ease-out;
}

button[type="submit"]:hover::before {
    width: 300px;
    height: 300px;
}

/* 自定义输入框样式 */
:deep(.ant-input) {
    height: 44px;
    font-size: 14px;
}

:deep(.ant-input-affix-wrapper) {
    padding: 0 11px;
    height: 44px;
    font-size: 14px;
    border-color: #d1d5db;
    position: relative;
}

:deep(.ant-input-affix-wrapper:focus),
:deep(.ant-input-affix-wrapper-focused) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

:deep(.ant-input-affix-wrapper:focus)::before,
:deep(.ant-input-affix-wrapper-focused)::before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background: rgba(59, 130, 246, 0.6);
    border-radius: 50%;
    filter: blur(3px);
    animation: borderAnimation 2s linear infinite;
    z-index: 1;
}

@keyframes borderAnimation {
    0% {
        top: -5px;
        left: -5px;
    }

    25% {
        top: -5px;
        left: calc(100% - 5px);
    }

    50% {
        top: calc(100% - 5px);
        left: calc(100% - 5px);
    }

    75% {
        top: calc(100% - 5px);
        left: -5px;
    }

    100% {
        top: -5px;
        left: -5px;
    }
}

:deep(.ant-input-affix-wrapper:hover) {
    border-color: #3b82f6;
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

:deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner),
:deep(.ant-checkbox:hover .ant-checkbox-inner),
:deep(.ant-checkbox-input:focus + .ant-checkbox-inner) {
    border-color: #3b82f6;
}

/* 输入框动画效果 */
:deep(.ant-input-affix-wrapper),
:deep(.ant-input) {
    transition: all 0.3s ease;
}

/* 自定义复选框动画 */
:deep(.ant-checkbox-checked .ant-checkbox-inner::after) {
    transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}
</style>