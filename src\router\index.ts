import { createRouter, create<PERSON>eb<PERSON><PERSON>ory, RouteRecordRaw } from 'vue-router';
import Home from '../views/Home.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/login.vue')
  },
  {
    path: '/',
    component: () => import('../components/layout/BasicLayout.vue'),
    children: [
      {
        path: '',
        name: 'home',
        component: Home
      },
      {
        path: 'ai-efficiency',
        name: 'ai-efficiency',
        component: () => import('../views/AIEfficiency/AIEfficiency.vue'),
        redirect: '/ai-efficiency/apps',
        meta: { breadcrumb: 'AI提效' },
        children: [
          {
            path: 'models',
            name: 'ai-models',
            component: () => import('../views/AIEfficiency/AIEfficiencyModels.vue'),
            meta: { breadcrumb: 'AI模型' }
          },
          {
            path: 'apps',
            name: 'ai-apps',
            component: () => import('../views/AIEfficiency/AIEfficiencyApps.vue'),
            meta: { breadcrumb: 'AI应用' }
          },
          {
            path: 'knowledge',
            name: 'knowledge-base',
            component: () => import('../views/AIEfficiency/AIEfficiencyKnowledge.vue'),
            meta: { breadcrumb: '知识库' }
          },
          {
            path: 'mcp',
            name: 'ai-mcp',
            component: () => import('../views/AIEfficiency/AIEfficiencyMCP.vue'),
            meta: { breadcrumb: 'MCP' }
          }
        ]
      },
      {
        path: 'test-tools',
        name: 'test-tools',
        component: () => import('../views/TestTool/TestTool.vue'),
        redirect: '/test-tools/platform',
        meta: { breadcrumb: '测试工具' },
        children: [
          {
            path: 'platform',
            name: 'test-platform',
            component: () => import('../views/TestTool/TestToolPlatform.vue'),
            meta: { breadcrumb: '测试平台' }
          },
          {
            path: 'statistics',
            component: () => import('../views/TestTool/TestToolStatistics.vue'),
            meta: { breadcrumb: '度量看板' },
            children: [
              {
                path: '',
                name: 'test-statistics',
                component: () => import('../views/TestTool/TestToolStatisticsMain.vue'),
                meta: { breadcrumb: '度量看板' }
              },
              {
                path: 'gantt',
                name: 'test-statistics-gantt',
                component: () => import('../views/TestTool/dashboard/TestToolGantt.vue'),
                meta: { breadcrumb: '任务看板' }
              }
            ]
          },
          {
            path: 'settings',
            name: 'test-settings',
            component: () => import('../views/TestTool/TestToolSettings.vue'),
            meta: { breadcrumb: '设置' }
          },
          // 新增 ITR 菜单
          {
            path: 'itr',
            name: 'test-itr',
            component: () => import('../views/TestTool/ITR/ITR.vue'),
            redirect: '/test-tools/itr/analysis',
            meta: { breadcrumb: 'ITR' },
            children: [
              {
                path: 'analysis',
                name: 'test-itr-analysis',
                component: () => import('../views/TestTool/ITR/ITRAnalysis.vue'),
                meta: { breadcrumb: 'ITR分析表' }
              },
              {
                path: 'report',
                name: 'test-itr-report',
                component: () => import('../views/TestTool/ITR/ITRReport.vue'),
                meta: { breadcrumb: '数据填报' }
              },
              {
                path: 'feature-management',
                name: 'test-itr-feature-management',
                component: () => import('../views/TestTool/ITR/ITRFeatureManagement.vue'),
                meta: { breadcrumb: '特性管理' }
              }
            ]
          }
        ]
      },
      {
        path: 'system',
        name: 'system',
        component: () => import('../views/Settings/Settings.vue'),
        redirect: '/system/toolManagement',
        meta: { breadcrumb: '系统配置' },
        children: [
          {
            path: 'toolManagement',
            name: 'toolManagement',
            component: () => import('../views/Settings/ToolManagement.vue'),
            meta: { breadcrumb: '工具管理' }
          },
          {
            path: 'basicSettings',
            name: 'basicSettings',
            component: () => import('../views/Settings/BasicSettings.vue'),
            meta: { breadcrumb: '基本配置' },
            children: [
              {
                path: 'user-management',
                name: 'userManagement',
                component: () => import('../views/UserManagement/UserList.vue'),
                meta: { breadcrumb: '用户管理' }
              },
              {
                path: 'feature-management',
                name: 'featureManagement',
                component: () => import('../views/FeatureManagement/FeatureList.vue'),
                meta: { breadcrumb: '特性管理' }
              }
              
            ]
          },
        
        ]
      },
      {
        path: '/documents/:id/',
        name: 'ai-documents',
        component: () => import('../views/AIEfficiency/KnowledgeDocuments/KnowledgeDetail.vue'),
        props: true
      },
      {
        path: '/codder/:id/',
        name: 'ai-codder',
        component: () => import('../views/AIEfficiency/KnowledgeCodder/KnowledgeDetail.vue'),
        props: true
      },
      {
        path: '/relation/:id/',
        name: 'ai-relation',
        component: () => import('../views/AIEfficiency/KnowledgeRelation/KnowledgeDetail.vue'),
        props: true
      }
    ]
  },
  {
    path: '/oauth-callback',
    name: 'oauth-callback',
    component: () => import('../views/login.vue')
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 模拟判断用户是否登录的函数
function isAuthenticated() {
  // 检查本地存储中的 token 和 userInfo
  return localStorage.getItem('token')!== null && localStorage.getItem('userInfo')!== null;
}

// 生成OAuth认证URL的函数
function generateOAuthURL() {
  const state = Date.now().toString();
  const nonce = Date.now().toString() + Math.random().toString(36).substring(2, 11);

  // 保存state到localStorage用于回调验证
  localStorage.setItem('oauth_auth_state', state);

  // OAuth配置
  const oauthConfig = {
    client_id: 'UK59v8',
    auth_base_url: 'https://sso.das-security.cn',
    authorization_endpoint: '/iam/auth/oauth2/sso',
    redirect_uri: 'http://10.113.7.86:8080/login'
  };

  // 构建认证URL
  const authUrl = `${oauthConfig.auth_base_url}${oauthConfig.authorization_endpoint}?` +
    `redirect_uri=${encodeURIComponent(oauthConfig.redirect_uri)}&` +
    `client_id=${oauthConfig.client_id}&` +
    `state=${state}&` +
    `response_type=code&` +
    `nonce=${nonce}`;

  return authUrl;
}

// 添加全局前置守卫
router.beforeEach((to, _from, next) => {
  // 检查是否是OAuth回调（任何路径只要有code参数就认为是OAuth回调）
  if (to.query.code && to.query.state) {
    console.log('检测到OAuth回调，保存参数并跳转到登录页', {
      path: to.path,
      code: to.query.code,
      state: to.query.state
    });

    // 保存OAuth参数到localStorage
    if (to.query.code) {
      localStorage.setItem('oauth_callback_code', to.query.code as string);
    }
    if (to.query.state) {
      localStorage.setItem('oauth_callback_state', to.query.state as string);
    }
    if (to.query.error) {
      localStorage.setItem('oauth_callback_error', to.query.error as string);
    }
    if (to.query.error_description) {
      localStorage.setItem('oauth_callback_error_description', to.query.error_description as string);
    }

    // 设置标记表示这是OAuth回调
    localStorage.setItem('oauth_callback_pending', 'true');

    // 如果当前不在登录页面，跳转到登录页面
    if (to.name !== 'login') {
      next({ name: 'login' });
      return;
    } else {
      // 如果已经在登录页面，直接放行，让登录页面处理OAuth回调
      next();
      return;
    }
  }

  // 如果用户已认证，正常放行
  if (isAuthenticated()) {
    next();
    return;
  }

  // 如果访问的是登录页面且有pending的OAuth回调，允许访问
  if (to.name === 'login' && localStorage.getItem('oauth_callback_pending')) {
    next();
    return;
  }

  // 如果直接访问登录页面（没有OAuth回调），允许访问，显示登录界面
  if (to.name === 'login') {
    next();
    return;
  }

  // 检查是否是手动退出登录
  if (localStorage.getItem('manual_logout')) {
    console.log('检测到手动退出登录，跳转到登录页面');
    localStorage.removeItem('manual_logout');
    next({ name: 'login' });
    return;
  }

  // 对于未认证用户访问其他路由，直接重定向到OAuth认证中心
  console.log('未认证用户访问受保护路由，重定向到OAuth认证中心', { path: to.path, name: to.name });
  const authUrl = generateOAuthURL();
  window.location.href = authUrl;
});

export default router;
