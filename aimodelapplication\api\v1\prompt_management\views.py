# -*- coding: utf-8 -*-

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import json
import requests
from template.response_temp import BaseResponse
from logger import connection_logging

router = APIRouter(prefix="/prompt_management", tags=["提示词管理"])
logger = connection_logging()

# 请求模型
class TermDefinition(BaseModel):
    name: str
    definition: str

class BusinessPrompt(BaseModel):
    content: str

class PromptManagementRequest(BaseModel):
    businessIntroduction: str
    termDefinitions: List[TermDefinition]
    businessPrompts: List[BusinessPrompt]

class GeneratePrerequisiteRequest(BaseModel):
    businessIntroduction: str
    termDefinitions: List[TermDefinition]
    businessPrompts: List[BusinessPrompt]
    apiDocument: str  # 接口文档内容

# 阿里云百炼API配置
DASHSCOPE_API_KEY = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

class DashScopeService:
    """阿里云百炼API服务"""
    
    @staticmethod
    def call_llm(prompt: str, model: str = "qwen-turbo") -> str:
        """调用阿里云百炼大语言模型"""
        headers = {
            "Authorization": f"Bearer {DASHSCOPE_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": 0.1,
                "max_tokens": 2000,
                "top_p": 0.8
            }
        }
        
        try:
            response = requests.post(DASHSCOPE_API_URL, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()

            # 处理不同的API响应格式
            if result.get("output"):
                output = result["output"]

                # 新格式：直接返回text字段
                if "text" in output:
                    return output["text"]

                # 旧格式：choices数组格式
                elif "choices" in output and output["choices"]:
                    return output["choices"][0]["message"]["content"]

                else:
                    raise Exception(f"API返回格式异常: {result}")
            else:
                raise Exception(f"API返回格式异常: {result}")

        except requests.exceptions.RequestException as e:
            logger.error(f"调用阿里云百炼API失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"调用大语言模型失败: {str(e)}")
        except Exception as e:
            logger.error(f"处理API响应失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"处理模型响应失败: {str(e)}")

@router.post("/save", summary="保存提示词配置")
async def save_prompt_config(request: PromptManagementRequest):
    """保存提示词管理配置"""
    try:
        # 这里可以将配置保存到数据库或文件
        # 暂时返回成功响应
        logger.info(f"保存提示词配置: {request.dict()}")
        
        return BaseResponse(
            code=200,
            message="提示词配置保存成功",
            data={"saved": True}
        )
    except Exception as e:
        logger.error(f"保存提示词配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存失败: {str(e)}")

@router.get("/load", summary="加载提示词配置")
async def load_prompt_config():
    """加载提示词管理配置"""
    try:
        # 这里可以从数据库或文件加载配置
        # 暂时返回空的默认配置，让用户自己填写
        default_config = {
            "businessIntroduction": "",
            "termDefinitions": [
                {"name": "", "definition": ""}
            ],
            "businessPrompts": [
                {"content": ""}
            ]
        }

        return BaseResponse(
            code=200,
            message="加载提示词配置成功",
            data=default_config
        )
    except Exception as e:
        logger.error(f"加载提示词配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"加载失败: {str(e)}")

@router.post("/generate_prerequisite", summary="生成前置操作")
async def generate_prerequisite(request: GeneratePrerequisiteRequest):
    """根据提示词配置和接口文档生成前置操作"""
    try:
        # 构建完整的提示词
        prompt = build_complete_prompt(request)
        
        # 调用大语言模型
        result = DashScopeService.call_llm(prompt)
        
        logger.info(f"生成前置操作成功: {result}")
        
        return BaseResponse(
            code=200,
            message="生成前置操作成功",
            data={"prerequisite": result.strip()}
        )
    except Exception as e:
        logger.error(f"生成前置操作失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

def build_complete_prompt(request: GeneratePrerequisiteRequest) -> str:
    """构建完整的提示词"""
    
    # 构建名词定义部分
    term_definitions_text = ""
    for term in request.termDefinitions:
        if term.name and term.definition:
            term_definitions_text += f"## {term.name}：{term.definition}\n"
    
    # 构建额外提示部分
    business_prompts_text = ""
    for prompt in request.businessPrompts:
        if prompt.content:
            business_prompts_text += f"## {prompt.content}\n"
    
    # 完整提示词模板
    complete_prompt = f"""# 任务描述
根据接口传参判断，在执行接口前需要进行的前置操作

# 业务介绍
{request.businessIntroduction}

# 名词定义
{term_definitions_text}

# 额外提示
{business_prompts_text}

# 必须满足的判断依据
## 业务场景：参考业务介绍判断出执行当前接口时必须要进行的操作，例如：更新授权规则前必须要条件为创建授权规则
## 接口类型：接口类型为创建类型接口
## 非枚举值：字段类型为非枚举值类型
## 路径嵌入：在url中明文声明的变量所必须依赖的前置操作，例如：/api/{{projectId}} 必须依赖创建项目

# 补充依据
## 字段映射：$ref引用的实体对象标识字段（如assetId→资产ID）
## 接口类型：接口类型为创建类型接口

# 任务要求(必须满足) 
- 只考虑一级参数，不考虑嵌套参数。例如：只考虑类似account的一级参数,排除account.sshKeyI等二级参数
- 每个前置接口均需要文档中的参数作为依据
- 不要放过任何一个参数，保证每个参数的前置操作均需要识别
- 额外提示一定要满足

# 接口文档
{request.apiDocument}

# 输出要求
## 仅输出前置接口以及举证参数
## 不要输出原因和描述，只需要给出结果

## 参考输出结果如下：
创建项目（projectId）、创建用户（userName）、创建资产(assetsId)
"""
    
    return complete_prompt
