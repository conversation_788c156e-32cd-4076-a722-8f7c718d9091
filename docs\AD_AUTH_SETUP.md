# AD认证集成说明

## 概述

本项目已集成公司AD（Active Directory）统一认证功能，支持用户通过公司账号进行登录。认证基于OAuth2.0协议，使用授权码模式。

## 功能特性

- 支持账号密码和AD认证两种登录方式
- 基于OAuth2.0授权码流程
- 安全的state和nonce验证
- 自动用户信息匹配和本地存储
- 友好的用户界面和错误提示

## 配置步骤

### 1. 获取认证配置信息

联系公司IT部门或安全团队，获取以下信息：
- `client_id`: 应用客户端ID
- `client_secret`: 应用客户端密钥
- 确认回调地址: `https://your-domain.com/login`

### 2. 更新配置文件

编辑 `src/config/ad-auth.ts` 文件，替换以下配置：

```typescript
export const AD_AUTH_CONFIG = {
  // 替换为实际的配置值
  client_id: 'your_actual_client_id',
  client_secret: 'your_actual_client_secret',
  
  // 其他配置保持不变...
};
```

### 3. 后端接口实现

需要在后端实现以下接口：

#### 3.1 AD Token交换接口
```
POST /api/AuthUser/ad-token-exchange
```

请求参数：
```json
{
  "code": "授权码",
  "client_id": "客户端ID",
  "client_secret": "客户端密钥",
  "grant_type": "authorization_code"
}
```

该接口需要：
1. 调用AD认证服务的token端点获取access_token
2. 使用access_token获取用户信息
3. 返回用户信息

#### 3.2 AD登录接口
```
POST /api/AuthUser/ad-login
```

请求参数：
```json
{
  "userInfo": {
    "sub": "用户唯一标识",
    "account": "用户账号",
    "name": "用户姓名",
    "email": "用户邮箱",
    // 其他用户信息...
  }
}
```

该接口需要：
1. 根据AD用户信息匹配本地用户
2. 生成JWT token
3. 返回登录结果

## 认证流程

1. 用户选择"AD认证"登录方式
2. 点击"AD统一认证登录"按钮
3. 跳转到公司认证中心
4. 用户输入公司账号密码
5. 认证成功后回调到登录页面
6. 前端获取授权码，调用后端接口
7. 后端验证并返回用户信息
8. 前端保存用户信息到localStorage
9. 跳转到主页面

## 安全考虑

- 使用state参数防止CSRF攻击
- 使用nonce参数防止重放攻击
- client_secret仅在后端使用，不暴露给前端
- 所有认证请求使用HTTPS

## 故障排除

### 常见问题

1. **配置不完整错误**
   - 检查 `src/config/ad-auth.ts` 中的配置是否正确
   - 确认client_id和client_secret已正确设置

2. **认证跳转失败**
   - 检查网络连接
   - 确认认证服务器地址是否正确

3. **回调验证失败**
   - 检查回调地址是否在AD应用中正确配置
   - 确认state参数验证逻辑

4. **用户匹配失败**
   - 检查后端用户匹配逻辑
   - 确认AD用户信息字段映射

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console中的错误信息
3. 检查Network面板中的API请求
4. 验证localStorage中的认证状态

## 开发注意事项

- 开发环境可能需要配置CORS
- 确保回调地址在所有环境中都正确配置
- 测试时注意清理localStorage中的认证状态
- 建议在测试环境先验证完整流程

## 联系支持

如遇到问题，请联系：
- 技术支持：[技术团队邮箱]
- IT支持：[IT部门邮箱]
