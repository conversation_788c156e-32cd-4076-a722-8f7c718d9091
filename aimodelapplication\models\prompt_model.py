# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from datetime import datetime
import json

class PromptConfiguration:
    """提示词配置模型"""
    
    def __init__(self):
        self.id: Optional[int] = None
        self.knowledge_base_id: str = ""
        self.business_introduction: str = ""
        self.created_at: Optional[datetime] = None
        self.updated_at: Optional[datetime] = None
        self.created_by: Optional[str] = None
        self.updated_by: Optional[str] = None
        self.is_deleted: bool = False
        
        # 关联数据
        self.term_definitions: List[Dict[str, Any]] = []
        self.business_prompts: List[Dict[str, Any]] = []
        self.extra_evidence: List[Dict[str, Any]] = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "knowledge_base_id": self.knowledge_base_id,
            "business_introduction": self.business_introduction,
            "term_definitions": self.term_definitions,
            "business_prompts": self.business_prompts,
            "extra_evidence": self.extra_evidence,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "created_by": self.created_by,
            "updated_by": self.updated_by
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PromptConfiguration':
        """从字典创建实例"""
        instance = cls()
        instance.id = data.get("id")
        instance.knowledge_base_id = data.get("knowledge_base_id", "")
        instance.business_introduction = data.get("business_introduction", "")
        instance.term_definitions = data.get("term_definitions", [])
        instance.business_prompts = data.get("business_prompts", [])
        instance.extra_evidence = data.get("extra_evidence", [])
        instance.created_by = data.get("created_by")
        instance.updated_by = data.get("updated_by")
        
        # 处理时间字段
        if data.get("created_at"):
            if isinstance(data["created_at"], str):
                instance.created_at = datetime.fromisoformat(data["created_at"])
            else:
                instance.created_at = data["created_at"]
                
        if data.get("updated_at"):
            if isinstance(data["updated_at"], str):
                instance.updated_at = datetime.fromisoformat(data["updated_at"])
            else:
                instance.updated_at = data["updated_at"]
        
        return instance

class PromptDatabaseService:
    """提示词数据库服务类"""
    
    def __init__(self, db_connection=None):
        """
        初始化数据库服务
        
        Args:
            db_connection: 数据库连接对象
        """
        self.db = db_connection
    
    async def save_configuration(self, config: PromptConfiguration) -> int:
        """
        保存提示词配置
        
        Args:
            config: 提示词配置对象
            
        Returns:
            int: 配置ID
        """
        # 这里应该实现实际的数据库保存逻辑
        # 由于没有具体的数据库连接，这里只是示例代码
        
        # 1. 保存主配置
        config_sql = """
        INSERT INTO prompt_configurations 
        (knowledge_base_id, business_introduction, created_by, updated_by)
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        business_introduction = VALUES(business_introduction),
        updated_by = VALUES(updated_by),
        updated_at = CURRENT_TIMESTAMP
        """
        
        # 2. 保存名词定义
        term_sql = """
        INSERT INTO prompt_term_definitions 
        (config_id, term_name, term_definition, sort_order)
        VALUES (%s, %s, %s, %s)
        """
        
        # 3. 保存业务提示
        prompt_sql = """
        INSERT INTO prompt_business_prompts 
        (config_id, prompt_content, sort_order)
        VALUES (%s, %s, %s)
        """
        
        # 4. 保存额外依据
        evidence_sql = """
        INSERT INTO prompt_extra_evidence 
        (config_id, evidence_name, evidence_description, sort_order)
        VALUES (%s, %s, %s, %s)
        """
        
        # 实际实现时需要使用事务确保数据一致性
        # 这里返回模拟的配置ID
        return 1
    
    async def load_configuration(self, knowledge_base_id: str) -> Optional[PromptConfiguration]:
        """
        加载提示词配置
        
        Args:
            knowledge_base_id: 知识库ID
            
        Returns:
            PromptConfiguration: 配置对象，如果不存在则返回None
        """
        # 这里应该实现实际的数据库查询逻辑
        
        query_sql = """
        SELECT 
            pc.id,
            pc.knowledge_base_id,
            pc.business_introduction,
            pc.created_at,
            pc.updated_at,
            pc.created_by,
            pc.updated_by
        FROM prompt_configurations pc
        WHERE pc.knowledge_base_id = %s AND pc.is_deleted = 0
        """
        
        # 查询关联数据的SQL
        term_sql = """
        SELECT term_name, term_definition, sort_order
        FROM prompt_term_definitions
        WHERE config_id = %s AND is_deleted = 0
        ORDER BY sort_order
        """
        
        prompt_sql = """
        SELECT prompt_content, sort_order
        FROM prompt_business_prompts
        WHERE config_id = %s AND is_deleted = 0
        ORDER BY sort_order
        """
        
        evidence_sql = """
        SELECT evidence_name, evidence_description, sort_order
        FROM prompt_extra_evidence
        WHERE config_id = %s AND is_deleted = 0
        ORDER BY sort_order
        """
        
        # 实际实现时需要执行这些SQL查询
        # 这里返回None表示未找到配置
        return None
    
    async def delete_configuration(self, knowledge_base_id: str) -> bool:
        """
        删除提示词配置（软删除）
        
        Args:
            knowledge_base_id: 知识库ID
            
        Returns:
            bool: 删除是否成功
        """
        # 软删除主配置
        delete_sql = """
        UPDATE prompt_configurations 
        SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP
        WHERE knowledge_base_id = %s
        """
        
        # 实际实现时需要执行SQL
        return True
    
    async def list_configurations(self, page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        分页查询提示词配置列表
        
        Args:
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 包含配置列表和分页信息
        """
        list_sql = """
        SELECT 
            pc.id,
            pc.knowledge_base_id,
            pc.business_introduction,
            pc.created_at,
            pc.updated_at,
            pc.created_by,
            pc.updated_by,
            COUNT(ptd.id) as term_count,
            COUNT(pbp.id) as prompt_count,
            COUNT(pee.id) as evidence_count
        FROM prompt_configurations pc
        LEFT JOIN prompt_term_definitions ptd ON pc.id = ptd.config_id AND ptd.is_deleted = 0
        LEFT JOIN prompt_business_prompts pbp ON pc.id = pbp.config_id AND pbp.is_deleted = 0
        LEFT JOIN prompt_extra_evidence pee ON pc.id = pee.config_id AND pee.is_deleted = 0
        WHERE pc.is_deleted = 0
        GROUP BY pc.id
        ORDER BY pc.updated_at DESC
        LIMIT %s OFFSET %s
        """
        
        count_sql = """
        SELECT COUNT(*) as total
        FROM prompt_configurations
        WHERE is_deleted = 0
        """
        
        # 实际实现时需要执行这些SQL查询
        return {
            "total": 0,
            "page": page,
            "page_size": page_size,
            "data": []
        }
