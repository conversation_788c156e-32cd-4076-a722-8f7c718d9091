# TAM零信任身份服务中心OAuth2.0集成指南

## 概述

本文档详细说明如何集成TAM零信任身份服务中心的OAuth2.0认证功能，实现单点登录（SSO）。

## 🔄 OAuth2.0认证流程

### 标准授权码流程

1. **用户发起登录** - 用户点击"统一认证登录"
2. **跳转认证服务器** - 应用跳转到TAM认证中心
3. **用户认证** - 用户在TAM中心输入凭据
4. **返回授权码** - TAM返回授权码到应用回调地址
5. **换取访问令牌** - 后端使用授权码换取访问令牌
6. **获取用户信息** - 使用访问令牌获取用户信息
7. **本地用户匹配** - 匹配或创建本地用户
8. **生成会话** - 生成JWT令牌完成登录

## 📋 申请应用注册

### 需要提供的信息

向TAM管理员申请应用注册时，需要提供：

```
应用名称: AI测试集成平台
应用描述: 人工智能测试和集成平台
回调地址: https://your-domain.com/login
应用类型: Web应用
权限范围: openid, profile, email
```

### 获得的配置信息

申请成功后，您将获得：

```
client_id: 应用客户端ID
client_secret: 应用客户端密钥
TAM服务器地址: https://tam.your-company.com
授权端点: /oauth2/authorize
令牌端点: /oauth2/token
用户信息端点: /oauth2/userinfo
```

## 🔧 前端配置

### 1. 更新配置文件

编辑 `src/config/ad-auth.ts`：

```typescript
export const OAUTH_AUTH_CONFIG = {
  client_id: 'your_actual_client_id',
  client_secret: 'your_actual_client_secret', // 前端不使用，仅作占位
  auth_base_url: 'https://tam.your-company.com',
  authorization_endpoint: '/oauth2/authorize',
  token_endpoint: '/oauth2/token',
  userinfo_endpoint: '/oauth2/userinfo',
  redirect_uri: window.location.origin + '/login',
  response_type: 'code',
  grant_type: 'authorization_code',
  scope: 'openid profile email',
  state_length: 32,
  nonce_length: 32,
};
```

### 2. 测试配置

访问测试页面验证配置：
```
http://localhost:3000/ad-auth-test
```

## 🔧 后端配置

### 1. 环境变量配置

设置以下环境变量：

```bash
# OAuth2.0配置
export OAUTH_BASE_URL=https://tam.your-company.com
export OAUTH_CLIENT_ID=your_actual_client_id
export OAUTH_CLIENT_SECRET=your_actual_client_secret
export OAUTH_REDIRECT_URI=https://your-domain.com/login
```

### 2. 应用配置

在 `application.yml` 中激活OAuth配置：

```yaml
spring:
  profiles:
    include: ad

oauth:
  auth:
    base-url: ${OAUTH_BASE_URL}
    token-url: ${OAUTH_BASE_URL}/oauth2/token
    userinfo-url: ${OAUTH_BASE_URL}/oauth2/userinfo
    client-id: ${OAUTH_CLIENT_ID}
    client-secret: ${OAUTH_CLIENT_SECRET}
    redirect-uri: ${OAUTH_REDIRECT_URI}
```

### 3. 数据库准备（可选）

为更好的用户匹配，建议添加字段：

```sql
-- 扩展用户表
ALTER TABLE sys_users ADD COLUMN job_no VARCHAR(50) COMMENT '工号';
ALTER TABLE sys_users ADD COLUMN mobile VARCHAR(20) COMMENT '手机号';
ALTER TABLE sys_users ADD COLUMN oauth_sub VARCHAR(100) COMMENT 'OAuth用户标识';

-- 创建索引
CREATE INDEX idx_sys_users_email ON sys_users(email);
CREATE INDEX idx_sys_users_job_no ON sys_users(job_no);
CREATE INDEX idx_sys_users_oauth_sub ON sys_users(oauth_sub);
```

## 🔌 API接口

### OAuth登录接口

```
POST /api/AuthUser/oauth-login
Content-Type: application/json

请求参数:
{
  "code": "授权码",
  "state": "状态参数",
  "error": "错误码（可选）",
  "errorDescription": "错误描述（可选）"
}

成功响应:
{
  "code": 200,
  "message": "OAuth认证登录成功",
  "data": {
    "token": "JWT_TOKEN",
    "user": {
      "userId": "用户ID",
      "username": "用户名",
      "realName": "真实姓名",
      "email": "邮箱",
      "role": "角色",
      "userType": "oauth_user",
      "product": "产品权限"
    }
  }
}
```

## 🔐 用户匹配策略

系统按以下优先级匹配用户：

1. **OAuth标识匹配** - 通过`sub`字段匹配
2. **邮箱匹配** - 通过邮箱地址匹配
3. **工号匹配** - 通过员工工号匹配
4. **用户名匹配** - 通过用户名匹配

如果都没有匹配到，系统会自动创建新用户。

## 🧪 测试验证

### 1. 配置验证

使用测试组件检查配置：
```
http://localhost:3000/ad-auth-test
```

### 2. 完整流程测试

1. 访问登录页面
2. 选择"统一认证"
3. 点击"TAM统一认证登录"
4. 在TAM中心完成认证
5. 验证回调和登录结果

### 3. API测试

使用Postman测试OAuth登录接口：

```bash
curl -X POST http://localhost:8080/api/AuthUser/oauth-login \
  -H "Content-Type: application/json" \
  -d '{
    "code": "test_authorization_code",
    "state": "test_state_value"
  }'
```

## 🔧 故障排除

### 常见问题

1. **配置错误**
   - 检查client_id和client_secret
   - 验证TAM服务器地址
   - 确认回调地址配置

2. **网络问题**
   - 检查TAM服务器连通性
   - 验证防火墙设置
   - 确认SSL证书有效性

3. **用户匹配失败**
   - 检查用户匹配逻辑
   - 验证数据库字段映射
   - 确认用户信息格式

### 调试方法

1. **启用调试日志**
   ```yaml
   logging:
     level:
       com.qfnu.service.impl.ADAuthServiceImpl: DEBUG
       org.springframework.web.reactive.function.client: DEBUG
   ```

2. **检查网络请求**
   - 查看WebClient请求日志
   - 验证OAuth端点响应
   - 检查用户信息格式

3. **验证JWT令牌**
   - 使用jwt.io解析令牌
   - 检查令牌有效期
   - 验证签名算法

## 🚀 部署注意事项

### 生产环境配置

1. **HTTPS要求**
   - OAuth2.0要求使用HTTPS
   - 配置SSL证书
   - 更新回调地址为HTTPS

2. **安全配置**
   - 使用环境变量存储敏感信息
   - 配置CORS策略
   - 设置安全头部

3. **性能优化**
   - 配置连接池
   - 设置合理的超时时间
   - 启用缓存机制

### 监控和日志

1. **关键指标监控**
   - OAuth认证成功率
   - 用户匹配成功率
   - 响应时间

2. **日志记录**
   - 认证请求日志
   - 错误详情记录
   - 用户行为追踪

## 📞 技术支持

如遇到问题，请联系：
- 技术团队：[技术支持邮箱]
- TAM管理员：[TAM管理员联系方式]
- 项目负责人：[项目负责人联系方式]
