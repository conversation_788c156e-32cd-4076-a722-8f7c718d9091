# OAuth redirect_uri 参数问题修复

## 🔍 问题发现

通过对比Postman成功请求和后端代码失败的情况，发现关键差异：

### Postman成功请求参数：
```
grant_type: authorization_code
code: 7UKiTN+dVd+EFZvqTxTD...
client_id: UK59v8
client_secret: M02KuH
```
**注意：没有 redirect_uri 参数**

### 后端代码原始参数：
```
grant_type: authorization_code
code: 7UKiTN+dVd+EFZvqTxTD...
client_id: UK59v8
client_secret: ***
redirect_uri: http://***********:8080/oauth-callback
```
**注意：包含了 redirect_uri 参数**

## 🎯 问题分析

根据OAuth2.0规范，`redirect_uri` 参数在令牌交换时是**可选的**，但某些OAuth服务器（如TAM）可能：

1. **不支持令牌交换时的 redirect_uri 参数**
2. **对 redirect_uri 参数有特殊验证逻辑**
3. **在包含 redirect_uri 时会挂起请求**

## 🔧 修复方案

### 已实施的修改

**文件**: `aiplatformbackend/src/main/java/com/qfnu/service/impl/ADAuthServiceImpl.java`

**修改前**:
```java
formData.add("grant_type", "authorization_code");
formData.add("code", code);
formData.add("client_id", clientId);
formData.add("client_secret", clientSecret);
if (redirectUri != null) {
    formData.add("redirect_uri", redirectUri);  // 这行导致问题
}
```

**修改后**:
```java
formData.add("grant_type", "authorization_code");
formData.add("code", code);
formData.add("client_id", clientId);
formData.add("client_secret", clientSecret);
// 注意：根据TAM服务器的实际要求，令牌交换时可能不需要redirect_uri参数
// if (redirectUri != null) {
//     formData.add("redirect_uri", redirectUri);
// }
```

## 🧪 测试验证

### 1. 重启后端服务
```bash
cd aiplatformbackend
mvn spring-boot:run
```

### 2. 查看新的日志输出
现在请求参数应该显示：
```
请求参数:
  grant_type: authorization_code
  code: 7UKiTN+dVd+EFZvqTxTD...
  client_id: UK59v8
  client_secret: ***
```
**注意：不再包含 redirect_uri**

### 3. 测试OAuth流程
访问：`http://***********:8080/oauth-debug-test`

### 4. 预期结果
应该能看到：
```
开始发送HTTP请求到TAM服务器...
等待TAM服务器响应...
收到TAM服务器响应
Token响应:
  access_token: eyJhbGciOiJSUzI1NiI...
  token_type: Bearer
  expires_in: 3600
```

## 📋 OAuth2.0规范说明

### 授权码交换令牌的标准参数

**必需参数**:
- `grant_type`: 必须是 "authorization_code"
- `code`: 授权码
- `client_id`: 客户端标识符

**可选参数**:
- `redirect_uri`: 仅在授权请求中包含时才需要
- `client_secret`: 客户端密钥（推荐）

### TAM服务器的特殊要求

根据测试结果，TAM服务器在令牌交换时：
- ✅ **支持**: grant_type, code, client_id, client_secret
- ❌ **不支持**: redirect_uri（会导致请求挂起）

## 🔍 故障排查

### 如果修改后仍然有问题

1. **检查参数格式**:
   - 确保使用 `application/x-www-form-urlencoded`
   - 确保参数名称正确

2. **检查授权码**:
   - 授权码是否在有效期内（通常5-10分钟）
   - 授权码是否已被使用过

3. **检查客户端凭据**:
   - client_id 是否正确
   - client_secret 是否正确

### 如果需要恢复 redirect_uri

如果TAM服务器要求 redirect_uri，可以取消注释：
```java
if (redirectUri != null) {
    formData.add("redirect_uri", redirectUri);
}
```

## ✅ 验证成功标准

修复成功的标志：
1. **不再卡在"等待TAM服务器响应"**
2. **能够收到访问令牌**
3. **能够获取用户信息**
4. **完整的OAuth登录流程正常工作**

---

**重要提示**: 这个修改基于Postman成功测试的结果，移除了令牌交换时的 redirect_uri 参数，应该能解决TAM服务器响应挂起的问题。
