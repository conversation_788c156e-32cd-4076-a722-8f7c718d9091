# -*- coding: utf-8 -*-

from fastapi import Request
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from logger import connection_logging
from api.v1 import router
from template.response_temp import BaseResponse
import time
from typing import Callable
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import uuid
import fastapi_cdn_host

app = FastAPI()
fastapi_cdn_host.patch_docs(app)

logger = connection_logging()

# 中间件类
class LoggingMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable):
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        start_time = time.time()
        
        try:
            response = await call_next(request)
            process_time = time.time() - start_time

            # 只记录非重定向的请求
            if response.status_code != 307:
                logger.info(
                    f"Request ID: {request_id} | "
                    f"Method: {request.method} | "
                    f"Path: {request.url.path} | "
                    f"Status: {response.status_code} | "
                    f"Process Time: {process_time:.2f}s"
                )

            return response
            
        except Exception as e:
            logger.error(
                f"Request ID: {request_id} | "
                f"Method: {request.method} | "
                f"Path: {request.url.path} | "
                f"处理异常: {str(e)}"
            )
            
            # 返回错误响应
            error_response = BaseResponse(
                code=500,
                message=f"服务器内部错误: {str(e)}",
                data={"count": 0, "records": []}
            )
            return JSONResponse(
                status_code=500,
                content=error_response.dict()
            )

# 跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000", 
        "http://localhost:8080", 
        "http://127.0.0.1:3000", 
        "http://127.0.0.1:8080", 
        "http://************:3000", 
        "http://************:8080", 
        "http://************:9003", 
        "http://***********:8080",
        "http://***********:8081",
    ],
    allow_credentials=True,  # 允许携带凭证
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],  # 明确指定允许的方法
    allow_headers=["*"],  # 允许所有请求头
    expose_headers=["*"],  # 允许暴露所有响应头
    max_age=3600,  # 预检请求的缓存时间（秒）
)

# 注册中间件
app.add_middleware(LoggingMiddleware)

app.include_router(router)

if __name__ == "__main__":
    import uvicorn
    
    # 开发环境配置
    uvicorn.run(
        "main:app",
        host="***********",
        port=8081,
        log_config=None,
        reload=True,  # 开发环境启用热重载
    )
