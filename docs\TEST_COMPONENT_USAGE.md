# AD认证测试组件使用指南

## 访问测试组件的方法

### 方法1：独立测试页面（推荐）

直接访问测试页面：
```
http://localhost:3000/ad-auth-test
```

这个页面包含完整的测试功能和使用说明。

### 方法2：在登录页面显示测试组件

在登录页面URL后添加参数：
```
http://localhost:3000/login?test=true
```

或者在开发环境下，测试组件会自动显示在登录页面底部。

## 测试组件功能说明

### 1. 配置状态检查
- **绿色圆点 ✓ 完整**：配置已正确设置
- **红色圆点 ✗ 不完整**：配置缺少必要字段
- 显示缺少的具体字段名称

### 2. 认证URL生成测试
- 点击"生成认证URL"按钮
- 系统会生成完整的AD认证URL
- 可以复制URL到新标签页测试认证流程

### 3. 当前认证状态显示
- **State**: 当前保存的认证状态值
- **Nonce**: 当前保存的随机数值
- **URL参数**: 当前页面的URL参数（用于检查认证回调）

### 4. 清理功能
- 清理localStorage中的认证数据
- 清理URL参数
- 重置所有认证状态

## 使用步骤

### 第一步：检查配置
1. 访问测试页面
2. 查看"配置状态"部分
3. 如果显示"不完整"，需要先配置AD认证信息

### 第二步：配置AD认证信息
编辑 `src/config/ad-auth.ts` 文件：
```typescript
export const AD_AUTH_CONFIG = {
  client_id: 'your_actual_client_id',        // 替换为实际值
  client_secret: 'your_actual_client_secret', // 替换为实际值
  // 其他配置保持不变
};
```

### 第三步：测试URL生成
1. 配置完成后，刷新测试页面
2. 点击"生成认证URL"
3. 检查生成的URL是否正确
4. 可以复制URL在新标签页中测试

### 第四步：测试认证流程
1. 复制生成的认证URL
2. 在新标签页中打开
3. 如果配置正确，会跳转到AD认证页面
4. 认证成功后会回调到登录页面

### 第五步：检查回调处理
1. 认证回调后，查看"当前认证状态"
2. 检查URL参数是否包含code和state
3. 观察控制台日志了解处理过程

## 常见问题排查

### 配置不完整
- 确认client_id和client_secret已正确设置
- 检查是否还有默认的占位符值

### URL生成失败
- 检查浏览器控制台的错误信息
- 确认配置文件语法正确

### 认证跳转失败
- 检查网络连接
- 确认AD认证服务器地址正确
- 检查client_id是否有效

### 回调处理失败
- 检查回调地址配置
- 确认state参数验证逻辑
- 查看控制台错误信息

## 开发调试技巧

1. **使用浏览器开发者工具**
   - Console：查看错误和日志信息
   - Network：检查API请求
   - Application：查看localStorage数据

2. **清理测试数据**
   - 使用"清理认证数据"按钮
   - 或手动清理localStorage
   - 清理浏览器缓存

3. **URL参数调试**
   - 手动添加URL参数测试回调处理
   - 例如：`/login?code=test&state=test`

4. **分步测试**
   - 先测试URL生成
   - 再测试认证跳转
   - 最后测试回调处理

## 注意事项

- 测试组件仅用于开发和调试
- 生产环境建议移除测试组件
- 敏感信息（如client_secret）不要提交到版本控制
- 测试时注意清理认证状态避免干扰
