刷新图谱<template>
  <div class="document-management">
    <!-- 左侧容器 -->
    <div class="left-container">
      <!-- 文档列表容器 -->
      <div class="doc-list-container">
        <div class="section-header">
          <h3 class="container-title">历史上传</h3>
          <div class="header-actions">
            <a-upload
              :show-upload-list="false"
              :before-upload="handleBeforeUpload"
              accept=".json"
            >
              <a-button 
                type="primary" 
                class="search-btn" 
                size="small"
              >
                <i class="fas fa-upload"></i>
                上传
              </a-button>
            </a-upload>
          </div>
        </div>
        <div class="doc-list-content">
          <!-- 文档列表内容 -->
          <div class="doc-list">
            <div v-for="doc in filteredDocuments" :key="doc.id" class="doc-item" :class="{ 'active': selectedDocId === doc.id }" @click="selectDocument(doc.id)">
              <div class="doc-content">
                <div class="doc-header">
                  <span class="doc-title large-title">{{ doc.title }}</span>
                  <div class="doc-actions">
                    <a-tooltip title="查看接口">
                      <a-button type="text" class="action-btn" @click.stop="viewDocument(doc.id)">
                        <EyeOutlined />
                      </a-button>
                    </a-tooltip>
                    <a-tooltip title="删除">
                      <a-button type="text" class="action-btn" danger @click.stop="confirmDelete(doc.id)">
                        <DeleteOutlined />
                      </a-button>
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </div>
            <!-- 无数据时显示 -->
            <div v-if="filteredDocuments.length === 0" class="empty-list">
              <p>尚未上传文档</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 筛选条件容器 -->
      <div class="filter-container">
        <div class="section-header">
          <h3 class="container-title">数据概览</h3>
        </div>
        <div class="filter-content">
          <!-- 数据统计卡片 -->
          <div class="stats-card">
            <div class="stats-grid">
              <div class="stats-item">
                <div class="stats-label">总实体</div>
                <div class="stats-value">{{ entityTotal || 0 }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">待嵌入实体</div>
                <div class="stats-value">{{ pendingEntityCount || 0 }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">孤儿实体</div>
                <div class="stats-value">{{ orphanEntityCount || 0 }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">代码提取</div>
                <div class="stats-value">{{ codeExtractCount || 0 }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">AI提取</div>
                <div class="stats-value">{{ aiExtractCount || 0 }}</div>
              </div>
              <div class="stats-item">
                <div class="stats-label">待确认关系</div>
                <div class="stats-value">{{ pendingConfirmCount || 0 }}</div>
              </div>
            </div>
          </div>
          <!-- 预览按钮 -->
          <div class="preview-section">
            <div class="preview-buttons">
              <a-button type="primary" block @click="handlePromptManagementClick" class="prompt-management-btn">
                <i class="fas fa-cogs"></i>
                提示词管理
              </a-button>
              <a-button type="primary" block @click="handlePreviewApi">
                <i class="fas fa-code"></i>
                预览接口
              </a-button>
            </div>
            <div class="extract-buttons">
              <a-button type="primary" block @click="handleScriptExtract">
                <i class="fas fa-robot"></i>
                关系提取
              </a-button>
              <a-button type="primary" block @click="handleAIExtract">
                <i class="fas fa-file-code"></i>
                关系确认
              </a-button>
            </div>
          </div>

          <!-- 任务列表 -->
          <div class="task-list-section">
            <a-table
              :dataSource="taskList"
              :columns="taskColumns"
              :pagination="{
                pageSize: 2,
                showSizeChanger: false,
                showQuickJumper: false
              }"
              size="small"
              :scroll="{ y: 200 }"
              :loading="taskLoading"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'progress'">
                  <a-progress :percent="record.progress" size="small" />
                </template>
                <template v-if="column.key === 'action'">
                  <div class="action-buttons">
                    <a-button type="link" size="small" @click="handlePreviewTask(record)">
                      查看
                    </a-button>
                    <a-button type="link" size="small" danger @click="handleDeleteTask(record)">
                      删除
                    </a-button>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧容器 -->
    <div class="right-container">
      <div class="right-header">
        <h3 class="container-title">关系图谱</h3>
        <div class="header-actions">
          <a-button 
            type="primary" 
            class="refresh-btn" 
            @click="refreshGraph"
            size="small"
          >
            <i class="fas fa-sync-alt"></i>
            刷新图谱
          </a-button>
          <a-button 
            type="default"
            class="history-btn disabled-btn"
            @click="showHistoryVersions"
            size="small"
            :disabled="true"
          >
            <i class="fas fa-history"></i>
            历史版本
          </a-button>
          <!-- 版本归档功能暂未开放，开放时将下面代码解除注释 -->
          <!-- <a-button 
            type="primary"
            class="archive-btn"
            @click="archiveVersion"
            :disabled="!isVersionChanged"
            size="small"
          >
            <i class="fas fa-save"></i>
            版本归档
          </a-button> -->
          <a-button 
            type="primary"
            class="archive-btn disabled-btn"
            @click="archiveVersion"
            :disabled="true"
            size="small"
          >
            <i class="fas fa-save"></i>
            版本归档
          </a-button>
        </div>
      </div>
      <div class="right-content">
        <RelationMap 
          v-if="currentDocument || isGraphRefreshed" 
          ref="relationMapRef" 
          :document="currentDocument"
          :map-info="mapInfo"
        />
        <div v-else class="no-document-selected">
          <p>请从左侧列表选择一个文档</p>
        </div>
      </div>
    </div>
    
    <!-- 删除确认弹窗 -->
    <a-modal
      v-model:visible="deleteModalVisible"
      title="确认删除"
      :centered="true"
      :closable="true"
      :maskClosable="true"
      :keyboard="true"
      :footer="null"
    >
      <a-alert
        type="warning"
        message="删除文档仅删除文档本身和未嵌入的实体数据，已经归档的实体和关系数据将会保留。"
        banner
        style="margin-bottom: 16px"
      />
      <p>确定要删除文档 <b>{{ documentToDelete?.title || ''}}</b> 吗？</p>
      <div class="modal-footer">
        <a-button key="cancel" @click="deleteModalVisible = false">取消</a-button>
        <a-button key="confirm" type="primary" danger @click="handleDeleteConfirm">
          删除
        </a-button>
      </div>
    </a-modal>
    
    <!-- 版本归档弹窗 -->
    <a-modal
      v-model:visible="archiveModalVisible"
      title="版本归档"
      :centered="true"
      :maskClosable="false"
      :footer="null"
      style="width: 400px"
      destroyOnClose
    >
      <a-form :model="archiveForm" layout="vertical">
        <a-form-item label="版本号" required>
          <a-input
            v-model:value="archiveVersionValue"
            placeholder="请输入版本号（如 V1.0.4）"
          />
        </a-form-item>
        <a-form-item label="版本描述" required>
          <a-textarea
            v-model:value="archiveVersionDesc"
            placeholder="请输入版本描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
      <div class="modal-footer">
        <a-button key="cancel" @click="archiveModalVisible = false">取消</a-button>
        <a-button key="confirm" type="primary" @click="handleArchiveConfirm">
          确认归档
        </a-button>
      </div>
    </a-modal>
    
    <!-- 历史版本弹窗 -->
    <a-modal
      v-model:visible="historyModalVisible"
      title="历史版本"
      :centered="true"
      style="width:50%; margin-top: 0;"
      destroyOnClose
    >
      <div class="history-versions">
        <table class="history-table">
          <thead>
            <tr>
              <th style="width: 20%">版本号</th>
              <th style="width: 25%">归档日期</th>
              <th style="width: 35%">版本描述</th>
              <th style="width: 20%">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="version in paginatedHistoryVersions" :key="version.version">
              <td class="version-cell">
                <span class="version-badge">{{ version.version }}</span>
              </td>
              <td>{{ version.date }}</td>
              <td class="version-desc">{{ version.version_desc || '暂无描述' }}</td>
              <td class="action-cell">
                <div class="action-buttons">
                  <a-button 
                    type="link" 
                    class="rollback-btn" 
                    @click="rollbackVersion(version)"
                  >
                    <i class="fas fa-undo"></i>
                    回退
                  </a-button>
                  <a-button 
                    type="link" 
                    danger 
                    class="delete-btn" 
                    @click="deleteVersion(version)"
                  >
                    <i class="fas fa-trash"></i>
                    删除
                  </a-button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- 分页控件 -->
        <div class="pagination-container">
          <div class="pagination">
            <button 
              class="pagination-btn" 
              :disabled="currentPage === 1"
              @click="changePage(currentPage - 1)"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            <span class="pagination-info">{{ currentPage }} / {{ totalPages }}</span>
            <button 
              class="pagination-btn" 
              :disabled="currentPage === totalPages"
              @click="changePage(currentPage + 1)"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
      <template #footer>
        <a-button type="primary" @click="historyModalVisible = false">关闭</a-button>
      </template>
    </a-modal>
    
    <!-- 版本回退确认弹窗 -->
    <a-modal
      v-model:visible="rollbackModalVisible"
      title="确认恢复"
      :centered="true"
      :closable="true"
      :maskClosable="true"
      :footer="null"
      destroyOnClose
    >
      <p>确定要恢复到版本 <b>{{ versionToRollback?.version || '' }}</b> 吗？恢复后将覆盖当前版本数据。</p>
      <div class="modal-footer">
        <a-button @click="rollbackModalVisible = false">取消</a-button>
        <a-button type="primary" @click="handleRollbackConfirm">确认</a-button>
      </div>
    </a-modal>
    
    <!-- 版本删除确认弹窗 -->
    <a-modal
      v-model:visible="deleteVersionModalVisible"
      title="确认删除"
      :centered="true"
      :closable="true"
      :maskClosable="true"
      :footer="null"
      destroyOnClose
    >
      <p>确定要删除版本 <b>{{ versionToDelete?.version || '' }}</b> 吗？删除后将无法恢复。</p>
      <div class="modal-footer">
        <a-button @click="deleteVersionModalVisible = false">取消</a-button>
        <a-button type="primary" danger @click="handleDeleteVersionConfirm">删除</a-button>
      </div>
    </a-modal>

    <!-- 新增：实体详情弹窗 -->
    <a-modal
      v-model:visible="entityModalVisible"
      :title="entityModalTitle"
      width="90vw"
      :footer="null"
      @cancel="handleEntityModalClose"
      :body-style="{ maxHeight: '70vh', overflowY: 'auto', padding: '24px' }"
    >
      <div style="margin-bottom: 16px; display: flex; gap: 12px; align-items: center; justify-content: space-between;">
        <div style="display: flex; gap: 12px; align-items: center;">
          <a-select
            v-model:value="entityStatusFilter"
            mode="multiple"
            allow-clear
            placeholder="请选择状态"
            style="width: 180px"
            @change="onStatusFilterChange"
          >
            <a-select-option v-for="item in entityStatusOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
          <a-select
            v-model:value="entityTagFilter"
            mode="multiple"
            allow-clear
            placeholder="请选择标签"
            style="width: 180px"
            @change="onTagFilterChange"
          >
            <a-select-option v-for="item in entityTagOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
          </a-select>
          <a-input-search
            v-model:value="entityNameSearch"
            placeholder="搜索实体名"
            style="width: 200px"
            @search="onEntitySearch"
            allow-clear
          />
        </div>
        <div style="display: flex; gap: 8px;">
          <a-button 
            type="primary" 
            :disabled="!selectedEntityRowKeys.length"
            @click="handleEmbedSelected"
            class="embed-btn"
          >
            <template #icon><ApiOutlined /></template>
            接口嵌入
          </a-button>
          <a-button type="primary" @click="showColumnSettings" class="column-settings-btn">
            <template #icon><SettingOutlined /></template>
            自定义列
          </a-button>
        </div>
      </div>
      <a-table
        :columns="visibleColumns"
        :data-source="pagedEntityList"
        :pagination="{
          current: entityPage,
          pageSize: entityPageSize,
          pageSizeOptions: ['10', '20', '50'],
          showSizeChanger: true,
          showQuickJumper: true,
          onShowSizeChange: onEntityPageSizeChange,
          total: entityTotal,
          onChange: onEntityPageChange
        }"
        rowKey="id"
        bordered
        size="middle"
        :scroll="{ x: 'max-content', y: 'calc(70vh - 200px)' }"
        :row-selection="rowSelection"
      />
    </a-modal>

    <!-- 列设置弹窗 -->
    <a-modal
      v-model:visible="columnSettingsVisible"
      title="自定义列"
      width="400px"
      :footer="null"
      @cancel="columnSettingsVisible = false"
      class="column-settings-modal"
    >
      <div class="column-settings-content">
        <div class="column-settings-header">
          <a-checkbox
            :indeterminate="isIndeterminate"
            :checked="isAllSelected"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        </div>
        <div class="column-settings-body">
          <a-checkbox-group v-model:value="selectedColumns" style="width: 100%">
            <a-space direction="vertical" style="width: 100%">
              <a-checkbox 
                v-for="col in allColumns" 
                :key="col.key" 
                :value="col.key"
                class="column-checkbox"
              >
                {{ col.title }}
              </a-checkbox>
            </a-space>
          </a-checkbox-group>
        </div>
      </div>
      <div class="column-settings-footer">
        <a-button style="margin-right: 8px" @click="columnSettingsVisible = false">取消</a-button>
        <a-button type="primary" @click="handleColumnSettingsConfirm">确定</a-button>
      </div>
    </a-modal>

    <!-- API详情弹窗 -->
    <ApiWindow
      v-model:visible="apiModalVisible"
      :title="apiModalTitle"
      :api-list="apiList"
      @close="apiModalVisible = false"
      @embed="handleApiEmbed"
    />

    <!-- 组件窗口 - 已注释掉 -->
    <!-- <component-window
      ref="componentWindowRef"
      v-model:visible="componentWindowVisible"
      :title="componentWindowTitle"
      :data-model-list="componentDataModelList"
      @dataModelSelected="handleDataModelSelected"
      @refresh-graph="refreshGraph"
    /> -->

    <!-- 实体组件弹窗 -->
    <EntityWindow
      :visible="entityWindowVisible"
      @update:visible="val => entityWindowVisible = val"
      @close="handleEntityWindowClose"
      title="实体管理"
      :taskProgress="firstTaskProgress"
    />

    <!-- 新增：关系确认弹窗 -->
    <ConfirmWindow
      :visible="confirmWindowVisible"
      @update:visible="val => confirmWindowVisible = val"
      @close="confirmWindowVisible = false"
      title="关系确认"
      v-bind="currentTaskId ? { taskId: currentTaskId } : {}"
    />

    <!-- 提示词管理弹窗 -->
    <a-modal
      v-model:visible="promptManagementVisible"
      title="提示词管理"
      width="800px"
      :footer="null"
      @cancel="handlePromptManagementClose"
      :body-style="{ maxHeight: '70vh', overflowY: 'auto', padding: '24px' }"
    >
      <div class="prompt-management-content">
        <!-- 自动保存提示 -->
        <div class="auto-save-tip">
          <i class="fas fa-info-circle"></i>
          <span>您的输入内容会自动暂存，关闭弹窗后再次打开仍会保留</span>
        </div>
        <!-- 业务介绍 -->
        <div class="prompt-section">
          <div class="section-header-with-help">
            <h4 class="section-title">业务介绍</h4>
            <a-tooltip title="点击查看业务介绍的详细说明">
              <a-button type="text" class="help-icon" @click="showBusinessIntroHelp">
                <i class="fas fa-question-circle"></i>
              </a-button>
            </a-tooltip>
          </div>
          <a-textarea
            v-model:value="businessIntroduction"
            placeholder="请输入业务介绍..."
            :rows="4"
            class="prompt-textarea"
          />
        </div>

        <!-- 名词定义 -->
        <div class="prompt-section">
          <div class="section-header-with-help">
            <h4 class="section-title">名词定义</h4>
            <a-tooltip title="点击查看名词定义的详细说明">
              <a-button type="text" class="help-icon" @click="showTermDefinitionHelp">
                <i class="fas fa-question-circle"></i>
              </a-button>
            </a-tooltip>
          </div>
          <div class="term-definitions">
            <div v-for="(term, index) in termDefinitions" :key="index" class="term-item">
              <div class="term-inputs">
                <a-input
                  v-model:value="term.name"
                  placeholder="名词"
                  class="term-name-input"
                />
                <a-input
                  v-model:value="term.definition"
                  placeholder="名词定义"
                  class="term-definition-input"
                />
                <a-button type="text" danger @click="removeTermDefinition(index)" class="remove-btn">
                  <i class="fas fa-trash"></i>
                </a-button>
              </div>
            </div>
            <a-button type="dashed" block @click="addTermDefinition" class="add-term-btn">
              <i class="fas fa-plus"></i>
              添加名词定义
            </a-button>
          </div>
        </div>

        <!-- 业务提示 -->
        <div class="prompt-section">
          <div class="section-header-with-help">
            <h4 class="section-title">业务提示</h4>
            <a-tooltip title="点击查看业务提示的详细说明">
              <a-button type="text" class="help-icon" @click="showBusinessPromptHelp">
                <i class="fas fa-question-circle"></i>
              </a-button>
            </a-tooltip>
          </div>
          <div class="business-prompts">
            <div v-for="(prompt, index) in businessPrompts" :key="index" class="prompt-item">
              <div class="prompt-input-group">
                <a-textarea
                  v-model:value="prompt.content"
                  placeholder="请输入业务提示..."
                  :rows="3"
                  class="prompt-textarea"
                />
                <a-button type="text" danger @click="removeBusinessPrompt(index)" class="remove-btn">
                  <i class="fas fa-trash"></i>
                </a-button>
              </div>
            </div>
            <a-button type="dashed" block @click="addBusinessPrompt" class="add-prompt-btn">
              <i class="fas fa-plus"></i>
              添加业务提示
            </a-button>
          </div>
        </div>

        <!-- 接口文档输入区域 -->
        <div class="prompt-section">
          <div class="section-header-with-help">
            <h4 class="section-title">接口文档</h4>
            <a-tooltip title="请输入需要分析的接口文档内容">
              <a-button type="text" class="help-icon">
                <i class="fas fa-question-circle"></i>
              </a-button>
            </a-tooltip>
          </div>
          <a-textarea
            v-model:value="apiDocument"
            placeholder="请输入接口文档内容..."
            :rows="6"
            class="prompt-textarea"
          />
        </div>

        <!-- 生成结果显示区域 -->
        <div v-if="prerequisiteResult" class="prompt-section">
          <div class="section-header-with-help">
            <h4 class="section-title">生成结果</h4>
          </div>
          <div class="result-display">
            {{ prerequisiteResult }}
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <a-button @click="handlePromptManagementClose">取消</a-button>
        <a-button @click="handlePromptManagementSave">保存配置</a-button>
        <a-button
          type="primary"
          @click="handleGeneratePrerequisite"
          :loading="generateLoading"
          :disabled="!apiDocument.trim()"
        >
          生成前置操作
        </a-button>
      </div>
    </a-modal>

    <!-- 帮助信息弹窗 -->
    <a-modal
      v-model:visible="helpModalVisible"
      :title="helpModalTitle"
      width="600px"
      :footer="null"
      @cancel="helpModalVisible = false"
    >
      <div class="help-content" v-html="helpModalContent"></div>
      <div class="modal-footer">
        <a-button type="primary" @click="helpModalVisible = false">知道了</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive, nextTick, h, onUnmounted } from 'vue';
import { DeleteOutlined, EyeOutlined, SettingOutlined, ApiOutlined } from '@ant-design/icons-vue';
import RelationMap from './RelationMap.vue';
import { message, Modal } from 'ant-design-vue';
import http from '@/utils/ai/http';
import ApiWindow from './Management/ApiWindow.vue';
import ComponentWindow from './Management/ComponentWindow.vue';
import EntityWindow from './Management/EntityWindow.vue';
import ConfirmWindow from './Management/ConfirmWindow.vue'; // 引入ConfirmWindow

const props = defineProps({
  knowledgeBaseId: {
    type: String,
    required: true
  },
  knowledgeBaseMeta: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      title: '',
      type: '',
      entityCount: 0,
      relationCount: 0,
      description: '',
      version: 'V1.0.0',
      version_desc: '暂无描述'
    })
  }
});

const emit = defineEmits(['switch-to-recall-test']);

// Method 标签颜色映射
const methodColorMap = {
  'GET': '#52c41a',     // 绿色
  'POST': '#1890ff',    // 蓝色
  'PUT': '#faad14',     // 黄色
  'DELETE': '#ff4d4f',  // 红色
  'PATCH': '#722ed1',   // 紫色
  'HEAD': '#13c2c2',    // 青色
  'OPTIONS': '#eb2f96'  // 粉色
};

// 获取 Method 标签样式
const getMethodTagStyle = (method) => {
  const color = methodColorMap[method?.toUpperCase()] || '#d9d9d9';
  return {
    backgroundColor: `${color}15`,
    color: color,
    border: `1px solid ${color}30`,
    padding: '2px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    fontWeight: '500',
    display: 'inline-block',
    textAlign: 'center',
    minWidth: '60px'
  };
};

// 获取状态标签样式
const getStatusTagStyle = (status) => {
  const colorMap = {
    '未嵌入': 'default',
    '已嵌入': 'success',
    'pending': 'processing',
    'embedding': 'purple',
    'failed': 'error',
    'queued': 'blue'
  };
  const color = colorMap[status] || 'default';
  return {
    color: color,
    backgroundColor: `${color}15`, // 使用稍微透明的背景色
    border: `1px solid ${color}30`,
    padding: '2px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    fontWeight: '500',
    display: 'inline-block',
    textAlign: 'center',
    minWidth: '60px'
  };
};

// 使用父组件传递的元数据
const knowledgeBase = ref(props.knowledgeBaseMeta);
console.log('knowledgeBase:', knowledgeBase.value);

// 地图信息
const mapInfo = ref(null);

// 获取地图信息
const fetchMapInfo = async () => {
  try {
    // 动态获取 relation_id
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];
    
    if (!relationId) {
      message.error('无法获取知识库ID');
      return;
    }

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      return;
    }

    // 调用获取地图信息接口
    const res = await http.get(`/knowledge_api/${relationId}/map`, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId,
        version: knowledgeBase.value?.version || ''
      }
    });

    if (res.data.code === 200) {
      console.log('获取到的地图信息:', res.data);
      mapInfo.value = res.data;
    } else {
      console.error(res.data.message || '获取地图信息失败');
    }
  } catch (error) {
    console.error('获取地图信息失败:', error);
  }
};

// 监听knowledgeBase的变化
watch(() => props.knowledgeBaseMeta, (newVal) => {
  console.log('knowledgeBaseMeta changed:', newVal);
  knowledgeBase.value = newVal;
}, { deep: true });

// 示例文档列表数据 - 修改为响应式数组
const documentList = reactive([]);

// 获取文档列表
const fetchDocuments = async () => {
  // 从 URL 获取 relation_id
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  try {
    const res = await http.get(`/knowledge_api/${relationId}/get_doc_list`);

    if (res.data.code === 200) {
      // 更新文档列表，响应data为文件名数组
      documentList.splice(0, documentList.length, ...res.data.data.map((filename, idx) => ({
        id: idx + 1, // 用索引作为id
        title: filename,
        date: '',
        entityCount: 0,
        relationCount: 0,
        status: 'success'
      })));
    } else {
      console.error('获取文档列表失败:', res.data.message);
    }
  } catch (error) {
    console.error('获取文档列表失败:', error);
  }
};

// 文档状态筛选
const docStatusFilter = ref('success'); // 默认展示成功文档

// 筛选文档列表
const filteredDocuments = computed(() => {
  if (docStatusFilter.value === 'success') {
    return documentList.filter(doc => doc.status === 'success');
  } else if (docStatusFilter.value === 'pending') {
    return documentList.filter(doc => doc.status !== 'success');
  }
  return documentList.filter(doc => doc.status === 'success'); // 默认返回成功文档
});

// 选中的文档ID
const selectedDocId = ref(null);

// 当前选中的文档详情
const currentDocument = computed(() => {
  if (!selectedDocId.value) return null;
  return documentList.find(doc => doc.id === selectedDocId.value);
});

// 选择文档
const selectDocument = (id) => {
  selectedDocId.value = id;
};

// 编辑文档改为查看文档
const viewDocument = async (id) => {
  const doc = documentList.find(doc => doc.id === id);
  if (!doc) return;
  
  // 打开API弹窗
  apiModalVisible.value = true;
  apiModalTitle.value = `查看文档 - ${doc.title}`;
  
  // 获取relation_id
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  
  try {
    // 获取该文档的API列表
    const res = await http.get(`/knowledge_api/${relationId}/get_entiy_list`, {
      params: {
        document_name: doc.title
      }
    });
    
    if (res.data.code === 200) {
      apiList.value = res.data.data || [];
    } else {
      apiList.value = [];
      message.error(res.data.message || '获取API列表失败');
    }
  } catch (error) {
    apiList.value = [];
    message.error('获取API列表失败');
    console.error('获取API列表失败:', error);
  }
};

// 删除确认弹窗状态
const deleteModalVisible = ref(false);
const documentToDelete = ref(null);

// 确认删除
const confirmDelete = (id) => {
  documentToDelete.value = documentList.find(doc => doc.id === id);
  deleteModalVisible.value = true;
};

// 处理删除确认
const handleDeleteConfirm = async () => {
  if (!documentToDelete.value) return;
  const id = documentToDelete.value.id;
  const docName = documentToDelete.value.title;
  // 获取relation_id
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  try {
    const res = await http.delete(`/knowledge_api/${relationId}/delete_endpoints_doc`, {
      params: {
        document_name: docName
      }
    });
    if (res.data.code === 200) {
      // 从列表中移除文档
      const index = documentList.findIndex(doc => doc.id === id);
      if (index !== -1) {
        documentList.splice(index, 1);
        if (selectedDocId.value === id) {
          selectedDocId.value = null;
          if (filteredDocuments.value.length > 0) {
            selectedDocId.value = filteredDocuments.value[0].id;
          }
        }
        message.success('文档已删除');
      }
    } else {
      message.error(res.data.message || '删除文档失败');
    }
  } catch (error) {
    message.error('删除文档失败');
  }
  // 关闭弹窗
  deleteModalVisible.value = false;
  documentToDelete.value = null;
};

// 当状态筛选改变时，重置页码
watch(docStatusFilter, () => {
  // 如果选择了新的筛选条件，且当前选中的文档不在筛选结果中，则清除选中状态
  if (selectedDocId.value) {
    const isDocumentInFilteredList = filteredDocuments.value.some(doc => doc.id === selectedDocId.value);
    if (!isDocumentInFilteredList) {
      selectedDocId.value = null;
    }
  }
});

// 添加实体统计相关的响应式变量
const pendingEntityCount = ref(0);
const orphanEntityCount = ref(0);
const codeExtractCount = ref(0);
const aiExtractCount = ref(0);
const pendingConfirmCount = ref(0);
const entityTotal = ref(0);

// 获取实体统计信息
const get_count_info = async () => {
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  try {
    const res = await http.get(`/knowledge_api/${relationId}/get_entities_count`);
    if (res.data.code === 200) {
      const data = res.data.data || {};
      entityTotal.value = data.total_entities || 0;
      pendingEntityCount.value = data.pending_entities || 0;
      orphanEntityCount.value = data.orphaned_entities_count || 0;
      // codeExtractCount/aiExtractCount如接口有字段可补充，否则默认0
      codeExtractCount.value = 0;
      aiExtractCount.value = 0;
      pendingConfirmCount.value = data.pending_relations || 0;
    } else {
      message.error(res.data.message || '获取实体统计信息失败');
    }
  } catch (error) {
    message.error('获取实体统计信息失败');
    console.error('获取实体统计信息失败:', error);
  }
};

// 任务进度轮询相关
let taskProgressTimer = null;
const firstTaskProgress = ref(null);
const checkTaskProgress = async () => {
  // 强制刷新时，先清除现有的定时器，防止重复调用
  if (taskProgressTimer) {
    clearTimeout(taskProgressTimer);
    taskProgressTimer = null;
  }

  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) return;
  try {
    const res = await http.get(`/knowledge_api/${relationId}/task_progress`);
    // 每次调用都刷新任务列表
    if (res.data && Array.isArray(res.data.data)) {
      const tasks = res.data.data;
      taskList.value = tasks.map(item => ({
        id: item.task_id,
        progress: item.completion_rate,
        status: item.completion_rate === 100 ? '已完成' : (item.completion_rate > 0 ? '进行中' : '等待中'),
      }));

      // 并且继续轮询逻辑
      if (tasks.length > 0) {
        const firstTask = tasks[0];
        firstTaskProgress.value = firstTask.completion_rate;
        if (firstTask.completion_rate !== 100) {
          taskProgressTimer = setTimeout(checkTaskProgress, 3000);
        }
      } else {
        // 没有任务时，确保按钮可用
        firstTaskProgress.value = 100;
      }
    }
  } catch (e) {
    // 忽略异常，继续轮询
    taskProgressTimer = setTimeout(checkTaskProgress, 3000);
  }
};

onMounted(() => {
  fetchDocuments();
  fetchMapInfo();
  get_count_info(); // 新增：获取实体统计信息
  checkTaskProgress();
  // 初始默认选中第一个文档
  if (filteredDocuments.value.length > 0) {
    selectedDocId.value = filteredDocuments.value[0].id;
  }
});

onUnmounted(() => {
  if (taskProgressTimer) clearTimeout(taskProgressTimer);
  if (saveTimer) clearTimeout(saveTimer);
});

// 处理文件上传前的验证
const handleBeforeUpload = async (file) => {

  // 动态获取 relation_id
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];

  // 构造FormData
  const formData = new FormData();
  formData.append('file', file);

  // 显示加载提示
  const hide = message.loading({ content: '文件上传中...', key: 'uploading', duration: 0 });

  try {
    const res = await http.post(`/knowledge_api/${relationId}/endpoints_upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    hide();
    if (res.data.code === 200) {
      message.success('文件上传成功');
      // 上传成功后刷新文档列表
      fetchDocuments();
      // 可选：刷新文档列表等
    } else {
      message.error(res.data.message || '文件上传失败');
    }
  } catch (error) {
    hide();
    message.error('文件上传失败');
    console.error(error);
  }
  // 阻止默认上传
  return false;
};

// 引用关系图组件
const relationMapRef = ref(null);

// 刷新状态 - 初始为true，以便首次自动显示图谱
const isGraphRefreshed = ref(true);

// 刷新图谱
const refreshGraph = async () => {
  console.log('刷新图谱');
  isGraphRefreshed.value = true;
  
  try {
    // 重新获取地图信息
    await fetchMapInfo();
    
    // 如果已有图谱实例，则调用其刷新方法
    if (relationMapRef.value) {
      // 使用nextTick确保DOM更新后再调用子组件方法
      nextTick(() => {
        relationMapRef.value.refreshGraphData();
      });
    }
  } catch (error) {
    console.error('刷新图谱失败:', error);
    message.error('刷新图谱失败');
  }
};

// 关系提取
const handleScriptExtract = () => {
  entityWindowVisible.value = true;
};

// 修改智能提取方法
const handleAIExtract = async () => {
  currentTaskId.value = null; // 关系确认按钮进入时不传递task_id
  confirmWindowVisible.value = true; // 直接显示ConfirmWindow
  // 原有的API调用逻辑将移至ConfirmWindow内部处理，或者根据需求进行调整
};

// 添加EntityWindow关闭处理方法
const handleEntityWindowClose = () => {
  entityWindowVisible.value = false;
  checkTaskProgress(); // 统一调用此方法来刷新和轮询
};

// 修改Entity嵌入处理方法
const handleEntityEmbed = async (selectedEntities) => {
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }

  try {
    // 根据嵌入类型选择不同的接口
    const embedType = selectedEntities[0]?.embed_type;
    const endpoint = embedType === 'increment' 
      ? `/knowledge_api/${relationId}/embed_entities_increment`
      : `/knowledge_api/${relationId}/embed_entities_override`;

    const res = await http.post(endpoint, {
      entities: selectedEntities.map(entity => ({
        ...entity,
        embed_type: undefined // 移除嵌入类型标识
      }))
    });

    if (res.data.code === 200) {
      message.success(embedType === 'increment' ? '实体增量嵌入成功' : '实体覆盖嵌入成功');
      entityWindowVisible.value = false;
      // 刷新图谱
      refreshGraph();
    } else {
      message.error(res.data.message || '实体嵌入失败');
    }
  } catch (error) {
    message.error('实体嵌入失败');
    console.error('实体嵌入失败:', error);
  }
};

// 任务列表相关
const taskList = ref([]);
const taskLoading = ref(false); // 新增：任务列表加载状态
const currentTaskId = ref(null); // 新增：当前任务ID

const taskColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 40 },
  { title: '进度', dataIndex: 'progress', key: 'progress', width: 70 },
  { title: '操作', key: 'action', width: 60 }
];

const getStatusColor = (status) => {
  const colorMap = {
    '已完成': 'success',
    '进行中': 'processing',
    '等待中': 'default',
    '失败': 'error'
  };
  return colorMap[status] || 'default';
};

const handlePreviewTask = (record) => {
  console.log('预览任务:', record);
  currentTaskId.value = record.id; // 记录当前任务ID
  confirmWindowVisible.value = true;
};

const handleDeleteTask = (record) => {
  Modal.confirm({
    title: '确认删除任务',
    content: `确定要删除任务 ID: ${record.id} 吗？`,
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      // 实际删除任务的逻辑
      const currentPath = window.location.pathname;
      const relationId = currentPath.split('/relation/')[1];
      if (!relationId) {
        message.error('无法获取知识库ID');
        return;
      }
      try {
        const res = await http.delete(`/knowledge_api/${relationId}/delete_task?task_id=${record.id}`);
        if (res.data.code === 200) {
          const index = taskList.value.findIndex(task => task.id === record.id);
          if (index !== -1) {
            taskList.value.splice(index, 1);
            message.success('任务删除成功');
          } else {
            message.error('未找到该任务');
          }
        } else {
          message.error(res.data.message || '删除任务失败');
        }
      } catch (error) {
        message.error('删除任务失败');
      }
    },
    onCancel() {
      console.log('取消删除');
    },
  });
};

// API详情弹窗相关
const apiModalVisible = ref(false);
const apiModalTitle = ref('');
const apiList = ref([]); // API列表数据

// 预览接口
const handlePreviewApi = async () => {
  apiModalVisible.value = true;
  apiModalTitle.value = '预览接口';
  
  // 获取relation_id
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  
  try {
    // 获取API列表，不传递document_name参数
    const res = await http.get(`/knowledge_api/${relationId}/get_entiy_list`);
    
    if (res.data.code === 200) {
      apiList.value = res.data.data || [];
    } else {
      apiList.value = [];
      message.error(res.data.message || '获取API列表失败');
    }
  } catch (error) {
    apiList.value = [];
    message.error('获取API列表失败');
    console.error('获取API列表失败:', error);
  }
};

// 获取API列表
const fetchApiList = async () => {
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  try {
    const res = await http.get(`/knowledge_api/${relationId}/get_api_list`);
    if (res.data.code === 200) {
      apiList.value = res.data.data || [];
    } else {
      apiList.value = [];
      message.error(res.data.message || '获取API列表失败');
    }
  } catch (error) {
    apiList.value = [];
    message.error('获取API列表失败');
    console.error('获取API列表失败:', error);
  }
};

// 修改API嵌入处理方法
const handleApiEmbed = async (selectedIds) => {
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }

  try {
    const res = await http.post(`/knowledge_api/${relationId}/entities`, {
      entity_list: selectedIds
    });

    if (res.data.code === 200) {
      message.success('接口嵌入成功');
      apiModalVisible.value = false;
      // 刷新图谱
      refreshGraph();
    } else {
      message.error(res.data.message || '接口嵌入失败');
    }
  } catch (error) {
    message.error('接口嵌入失败');
    console.error('接口嵌入失败:', error);
  }
};

// 组件窗口相关
const componentWindowVisible = ref(false);
const componentWindowTitle = ref('');
const componentDataModelList = ref([]); // 数据模型列表
const componentWindowRef = ref(null);

const handleDataModelClick = () => {
  componentWindowVisible.value = true;
  componentWindowTitle.value = '数据模型列表';
  componentWindowRef.value?.showDataModelWindow();
};

const handleDataModelSelected = (data) => {
  console.log('选中的数据模型详情:', data);
  // 处理数据模型详情
};

const entityWindowVisible = ref(false);
const confirmWindowVisible = ref(false); // 控制ConfirmWindow可见性

// 提示词管理相关
const promptManagementVisible = ref(false);
const businessIntroduction = ref('');
const termDefinitions = ref([{ name: '', definition: '' }]);
const businessPrompts = ref([{ content: '' }]);
const apiDocument = ref('');
const prerequisiteResult = ref('');
const generateLoading = ref(false);

// 监听数据变化，自动保存到本地存储
let saveTimer = null;
const debouncedSave = () => {
  if (saveTimer) {
    clearTimeout(saveTimer);
  }
  saveTimer = setTimeout(() => {
    saveToLocalStorage();
  }, 1000); // 1秒后保存，避免频繁保存
};

// 监听输入变化
watch([businessIntroduction, termDefinitions, businessPrompts, apiDocument], () => {
  debouncedSave();
}, { deep: true });

// 帮助弹窗相关
const helpModalVisible = ref(false);
const helpModalTitle = ref('');
const helpModalContent = ref('');

// 提示词管理方法
const handlePromptManagementClick = () => {
  promptManagementVisible.value = true;
  // 加载已保存的提示词数据或本地暂存数据
  loadPromptData();
};

const handlePromptManagementClose = () => {
  // 关闭弹窗时自动暂存当前输入的内容
  saveToLocalStorage();
  promptManagementVisible.value = false;
};

const handlePromptManagementSave = async () => {
  try {
    const promptData = {
      businessIntroduction: businessIntroduction.value,
      termDefinitions: termDefinitions.value.filter(term => term.name || term.definition),
      businessPrompts: businessPrompts.value.filter(prompt => prompt.content)
    };

    // 调用后端API保存数据
    const res = await http.post('prompt_management/save', promptData);

    if (res.data.code === 200) {
      message.success('提示词配置保存成功');
      // 保存成功后清除本地暂存数据
      clearLocalStorage();
    } else {
      message.error(res.data.message || '保存失败');
    }
  } catch (error) {
    message.error('保存失败');
    console.error('保存提示词失败:', error);
  }
};

// 本地存储键名
const LOCAL_STORAGE_KEY = 'prompt_management_temp_data';

// 保存到本地存储
const saveToLocalStorage = () => {
  try {
    const tempData = {
      businessIntroduction: businessIntroduction.value,
      termDefinitions: termDefinitions.value,
      businessPrompts: businessPrompts.value,
      apiDocument: apiDocument.value,
      timestamp: Date.now()
    };
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(tempData));
  } catch (error) {
    console.error('保存到本地存储失败:', error);
  }
};

// 从本地存储加载
const loadFromLocalStorage = () => {
  try {
    const tempDataStr = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (tempDataStr) {
      const tempData = JSON.parse(tempDataStr);
      // 检查数据是否过期（7天）
      const isExpired = Date.now() - tempData.timestamp > 7 * 24 * 60 * 60 * 1000;

      if (!isExpired) {
        businessIntroduction.value = tempData.businessIntroduction || '';
        termDefinitions.value = tempData.termDefinitions || [{ name: '', definition: '' }];
        businessPrompts.value = tempData.businessPrompts || [{ content: '' }];
        apiDocument.value = tempData.apiDocument || '';
        return true; // 成功加载本地数据
      } else {
        // 清除过期数据
        localStorage.removeItem(LOCAL_STORAGE_KEY);
      }
    }
  } catch (error) {
    console.error('从本地存储加载失败:', error);
  }
  return false; // 没有本地数据或加载失败
};

// 清除本地暂存数据
const clearLocalStorage = () => {
  try {
    localStorage.removeItem(LOCAL_STORAGE_KEY);
  } catch (error) {
    console.error('清除本地存储失败:', error);
  }
};

// 加载提示词数据
const loadPromptData = async () => {
  // 首先尝试加载本地暂存数据
  const hasLocalData = loadFromLocalStorage();

  if (!hasLocalData) {
    // 如果没有本地数据，则从服务器加载
    try {
      const res = await http.get('prompt_management/load');

      if (res.data.code === 200) {
        const data = res.data.data;
        businessIntroduction.value = data.businessIntroduction || '';
        termDefinitions.value = data.termDefinitions || [{ name: '', definition: '' }];
        businessPrompts.value = data.businessPrompts || [{ content: '' }];
        apiDocument.value = ''; // 接口文档不从服务器加载
      }
    } catch (error) {
      console.error('加载提示词数据失败:', error);
      // 使用空白默认值
      businessIntroduction.value = '';
      termDefinitions.value = [{ name: '', definition: '' }];
      businessPrompts.value = [{ content: '' }];
      apiDocument.value = '';
    }
  }
};

// 名词定义相关方法
const addTermDefinition = () => {
  termDefinitions.value.push({ name: '', definition: '' });
};

const removeTermDefinition = (index) => {
  if (termDefinitions.value.length > 1) {
    termDefinitions.value.splice(index, 1);
  }
};

// 业务提示相关方法
const addBusinessPrompt = () => {
  businessPrompts.value.push({ content: '' });
};

const removeBusinessPrompt = (index) => {
  if (businessPrompts.value.length > 1) {
    businessPrompts.value.splice(index, 1);
  }
};

// 帮助信息方法
const showBusinessIntroHelp = () => {
  helpModalTitle.value = '业务介绍说明';
  helpModalContent.value = `
    <div>
      <h4>业务介绍</h4>
      <p>业务介绍用于描述当前知识库所涉及的业务领域和背景信息。</p>
      <ul>
        <li>简要描述业务场景和应用领域</li>
        <li>说明知识库的主要用途和目标</li>
        <li>提供必要的背景信息帮助理解业务上下文</li>
      </ul>
      <p><strong>示例：</strong></p>
      <div style="background-color: #f6f8fa; padding: 12px; border-radius: 4px; margin: 8px 0;">
        当前系统为堡垒机系统
      </div>
    </div>
  `;
  helpModalVisible.value = true;
};

const showTermDefinitionHelp = () => {
  helpModalTitle.value = '名词定义说明';
  helpModalContent.value = `
    <div>
      <h4>名词定义</h4>
      <p>名词定义用于解释业务中的专业术语和关键概念。</p>
      <ul>
        <li>定义业务领域中的专业术语</li>
        <li>确保术语理解的一致性</li>
        <li>为AI模型提供准确的概念解释</li>
      </ul>
      <p><strong>示例：</strong></p>
      <div style="background-color: #f6f8fa; padding: 12px; border-radius: 4px; margin: 8px 0;">
        <p style="margin: 4px 0;"><strong>资产：</strong>本地添加的资产，包括主机数据库等</p>
        <p style="margin: 4px 0;"><strong>账号：</strong>创建资产时同步创建或者绑定资产创建</p>
        <p style="margin: 4px 0;"><strong>标签：</strong>分为资产标签和账号标签</p>
        <p style="margin: 4px 0;"><strong>授权规则：</strong>可将资产以及资产账号对用户授权</p>
        <p style="margin: 4px 0;"><strong>策略：</strong>分为主机命令策略、数据库命令策略以及用户访问策略，可以关联策略到授权规则中</p>
      </div>
    </div>
  `;
  helpModalVisible.value = true;
};

const showBusinessPromptHelp = () => {
  helpModalTitle.value = '业务提示说明';
  helpModalContent.value = `
    <div>
      <h4>业务提示</h4>
      <p>业务提示用于指导AI模型在处理业务相关问题时的行为和输出风格。</p>
      <ul>
        <li>提供处理特定业务场景的指导原则</li>
        <li>定义输出格式和风格要求</li>
        <li>设置业务规则和约束条件</li>
      </ul>
      <p><strong>示例：</strong></p>
      <div style="background-color: #f6f8fa; padding: 12px; border-radius: 4px; margin: 8px 0;">
        <p style="margin: 4px 0;">• 授权是对用户进行（资产，资产账号等）授权</p>
        <p style="margin: 4px 0;">• 账号是关联在资产下，创建资产可以同时创建账号，也可以创建资产后再创建账号</p>
        <p style="margin: 4px 0;">• 目录只有资产存在目录，也叫资产节点，是树型结构</p>
        <p style="margin: 4px 0;">• 动态对象分为动态账号组、动态用户组</p>
      </div>
    </div>
  `;
  helpModalVisible.value = true;
};

// 生成前置操作
const handleGeneratePrerequisite = async () => {
  if (!apiDocument.value.trim()) {
    message.warning('请输入接口文档内容');
    return;
  }

  generateLoading.value = true;
  prerequisiteResult.value = '';

  try {
    const requestData = {
      businessIntroduction: businessIntroduction.value,
      termDefinitions: termDefinitions.value.filter(term => term.name || term.definition),
      businessPrompts: businessPrompts.value.filter(prompt => prompt.content),
      apiDocument: apiDocument.value
    };

    const res = await http.post('prompt_management/generate_prerequisite', requestData);

    if (res.data.code === 200) {
      prerequisiteResult.value = res.data.data.prerequisite;
      message.success('前置操作生成成功');
    } else {
      message.error(res.data.message || '生成失败');
    }
  } catch (error) {
    message.error('生成前置操作失败');
    console.error('生成前置操作失败:', error);
  } finally {
    generateLoading.value = false;
  }
};

</script>

<style scoped>
.document-management {
  width: 100%;
  height: 81vh;
  display: flex;
  gap: 12px;
  padding: 0.5%;
  box-sizing: border-box;
}

/* 左侧容器样式 */
.left-container {
  width: 16.67%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 文档列表容器 */
.doc-list-container {
  flex: 0.25;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 筛选条件容器 */
.filter-container {
  flex: 0.75;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 右侧容器样式 */
.right-container {
  width: 83.33%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 各容器头部样式 */
.section-header, .right-header {
  height: 42px;
  padding: 0 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

/* 容器标题样式 */
.container-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

/* 容器内容区域样式 */
.doc-list-content, .filter-content, .right-content {
  flex: 1;
  overflow-y: auto;
  padding: 4px;
}

/* 文档列表样式 */
.doc-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;
}

.doc-item {
  position: relative;
  padding: 0;
  margin-bottom: 4px;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: stretch;
  border: 1px solid #f0f0f0;
  height: 48px;
  min-height: 48px;
  background: #fff;
}

.doc-item:hover {
  border-color: #1890ff;
  background-color: #f0f7ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.doc-item.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.doc-content {
  flex: 1;
  padding: 6px 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.doc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  height: 100%;
  padding: 2px 0;
}

.doc-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
  width: 200px;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.large-title {
  font-size: 15px;
  font-weight: 600;
  color: #262626;
  margin-right: 16px;
  width: 200px;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.doc-item:hover .doc-title,
.doc-item:hover .large-title {
  color: #1890ff;
}

.doc-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
}

.doc-item:hover .doc-actions {
  opacity: 1;
  transform: translateX(0);
}

.action-btn {
  padding: 0;
  height: 22px;
  width: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 4px;
}

.action-btn:active {
  background-color: rgba(24, 144, 255, 0.2);
}

.action-btn :deep(.anticon) {
  font-size: 14px;
  transition: all 0.3s ease;
}

.action-btn:hover :deep(.anticon) {
  transform: scale(1.1);
}

/* 空列表状态优化 */
.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #bfbfbf;
  font-size: 14px;
}

/* 滚动条美化 */
.doc-list-content::-webkit-scrollbar {
  width: 6px;
}

.doc-list-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.doc-list-content::-webkit-scrollbar-track {
  background-color: transparent;
}

.doc-list-content:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
}

.no-document-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #bfbfbf;
  font-size: 16px;
}

/* 隐藏原生滚动条 */
.doc-list-content::-webkit-scrollbar,
.filter-content::-webkit-scrollbar,
.right-content::-webkit-scrollbar {
  width: 6px;
}

.doc-list-content::-webkit-scrollbar-track,
.filter-content::-webkit-scrollbar-track,
.right-content::-webkit-scrollbar-track {
  background: transparent;
}

.doc-list-content::-webkit-scrollbar-thumb,
.filter-content::-webkit-scrollbar-thumb,
.right-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.doc-list-content::-webkit-scrollbar-thumb:hover,
.filter-content::-webkit-scrollbar-thumb:hover,
.right-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

/* 头部操作区域 */
.header-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-btn {
  min-width: auto;
  line-height: 1;
  padding: 0;
}

.search-btn {
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
}

.search-btn i {
  font-size: 12px;
  margin-right: 4px;
}

.fas {
  font-size: 14px;
}

/* 弹窗底部样式 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  gap: 8px;
}

/* 头部按钮样式 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.archive-btn,
.history-btn,
.refresh-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 28px;
  font-size: 12px;
  padding: 0 10px;
  border-radius: 4px;
  transition: all 0.3s;
}

.archive-btn i,
.history-btn i,
.refresh-btn i {
  font-size: 12px;
  margin-right: 4px;
}

.archive-btn {
  background-color: #52c41a;
  border-color: #52c41a;
}

.archive-btn:hover {
  background-color: #73d13d;
  border-color: #73d13d;
}

.history-btn {
  color: #595959;
}

.history-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

/* 历史版本表格样式 */
.history-versions {
  margin-top: 8px;
}

.history-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  table-layout: fixed;
  margin-bottom: 16px;
}

.history-table th, 
.history-table td {
  padding: 14px 12px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
  word-break: break-all;
}

.history-table th {
  background-color: #fafafa;
  font-weight: 500;
  color: #262626;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.history-table th:first-child {
  border-left: 1px solid #f0f0f0;
  border-top-left-radius: 8px;
}

.history-table th:last-child {
  border-right: 1px solid #f0f0f0;
  border-top-right-radius: 8px;
}

.history-table td {
  background-color: white;
}

.history-table td:first-child {
  border-left: 1px solid #f0f0f0;
}

.history-table td:last-child {
  border-right: 1px solid #f0f0f0;
}

.history-table tr:last-child td:first-child {
  border-bottom-left-radius: 8px;
}

.history-table tr:last-child td:last-child {
  border-bottom-right-radius: 8px;
}

.history-table tr:last-child td {
  border-bottom: 1px solid #f0f0f0;
}

.history-table tr:hover td {
  background-color: #fafafa;
}

.version-cell {
  white-space: nowrap;
}

.version-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #e6f7ff;
  color: #1890ff;
  font-size: 12px;
  font-weight: 500;
}

.action-cell {
  white-space: nowrap;
  text-align: center;
  padding: 8px 12px !important;
}

.action-buttons {
  display: flex;
  gap: 2px;
}

.action-buttons .ant-btn {
  padding: 0 2px;
  height: 24px;
  font-size: 12px;
}

.rollback-btn, 
.delete-btn {
  padding: 4px 8px;
  height: 28px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  white-space: nowrap;
}

.rollback-btn i, 
.delete-btn i {
  font-size: 12px;
}

.version-desc {
  max-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分页控件样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  margin-bottom: 8px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pagination-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination-btn:hover:not(:disabled) {
  color: #1890ff;
  border-color: #1890ff;
}

.pagination-btn:disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 14px;
  color: #595959;
}

.disabled-btn {
  position: relative;
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.disabled-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    z-index: 1;
  }

.disabled-btn i, 
.disabled-btn span {
    position: relative;
    z-index: 2;
}

/* 数据统计卡片样式 */
.stats-card {
  background: #fafafa;
  border-radius: 8px;
  padding: 12px 0;
  margin-bottom: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: default;
}

.stats-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-color: #1890ff;
  background-color: #f0f7ff;
}

.stats-item:hover .stats-value {
  color: #1890ff;
  transform: scale(1.05);
}

.stats-label {
  color: #595959;
  font-size: 13px;
  transition: color 0.3s ease;
}

.stats-value {
  color: #1890ff;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 修改预览按钮区域样式 */
.preview-section {
  padding: 0;
}

.preview-buttons,
.extract-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  margin-top: 8px;
}

.preview-section .ant-btn {
  flex: 1;
  height: 32px;
  font-size: 14px;
}

.preview-section .ant-btn i {
  margin-right: 8px;
}

/* 自定义列按钮样式 */
.column-settings-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.column-settings-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

/* 自定义列弹窗样式 */
.column-settings-modal :deep(.ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
}

.column-settings-modal :deep(.ant-modal-header) {
  margin-bottom: 0;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.column-settings-modal :deep(.ant-modal-title) {
  font-size: 16px;
  font-weight: 500;
}

.column-settings-content {
  padding: 16px 24px;
}

.column-settings-header {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.column-settings-body {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.column-settings-body::-webkit-scrollbar {
  width: 6px;
}

.column-settings-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.column-settings-body::-webkit-scrollbar-track {
  background-color: transparent;
}

.column-checkbox {
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.column-checkbox:hover {
  background-color: #f5f5f5;
}

.column-settings-footer {
  padding: 16px 24px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 美化复选框样式 */
.column-settings-modal :deep(.ant-checkbox-wrapper) {
  font-size: 14px;
}

.column-settings-modal :deep(.ant-checkbox) {
  top: 0;
}

.column-settings-modal :deep(.ant-checkbox-inner) {
  border-radius: 4px;
}

.column-settings-modal :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.column-settings-modal :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner),
.column-settings-modal :deep(.ant-checkbox:hover .ant-checkbox-inner) {
  border-color: #1890ff;
}

/* Method 标签样式 */
.method-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
  transition: all 0.3s;
}

.method-tag:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

/* 在 style 部分添加以下样式 */
.embed-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.embed-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

.task-list-section {
  margin-top: 20px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.task-list-section .section-header {
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.task-list-section .container-title {
  font-size: 14px;
  margin: 0;
  line-height: 1.2;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.action-buttons .ant-btn {
  padding: 0 4px;
  height: 24px;
  font-size: 12px;
}

/* 提示词管理按钮样式 */
.prompt-management-btn {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
}

.prompt-management-btn:hover {
  background-color: #73d13d !important;
  border-color: #73d13d !important;
}

.prompt-management-btn:focus {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
}

/* 提示词管理弹窗样式 */
.prompt-management-content {
  padding: 0;
}

/* 自动保存提示样式 */
.auto-save-tip {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #1890ff;
}

.auto-save-tip i {
  font-size: 16px;
}

.prompt-section {
  margin-bottom: 24px;
}

.prompt-section:last-child {
  margin-bottom: 0;
}

.section-header-with-help {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.help-icon {
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  border-radius: 50%;
  transition: all 0.3s;
}

.help-icon:hover {
  background-color: #f0f7ff;
  color: #1890ff;
}

.help-icon i {
  font-size: 14px;
}

.prompt-textarea {
  width: 100%;
  resize: vertical;
}

/* 名词定义样式 */
.term-definitions {
  width: 100%;
}

.term-item {
  margin-bottom: 12px;
}

.term-inputs {
  display: flex;
  gap: 8px;
  align-items: center;
}

.term-name-input {
  flex: 0 0 150px;
}

.term-definition-input {
  flex: 1;
}

.remove-btn {
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff4d4f;
  border-radius: 4px;
  transition: all 0.3s;
}

.remove-btn:hover {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.add-term-btn,
.add-prompt-btn {
  margin-top: 12px;
  border-style: dashed;
  color: #1890ff;
  border-color: #d9d9d9;
  transition: all 0.3s;
}

.add-term-btn:hover,
.add-prompt-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.add-term-btn i,
.add-prompt-btn i {
  margin-right: 8px;
}

/* 业务提示样式 */
.business-prompts {
  width: 100%;
}

.prompt-item {
  margin-bottom: 12px;
}

.prompt-input-group {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.prompt-input-group .prompt-textarea {
  flex: 1;
}

.prompt-input-group .remove-btn {
  margin-top: 4px;
}

/* 帮助内容样式 */
.help-content {
  line-height: 1.6;
}

.help-content h4 {
  color: #262626;
  margin-bottom: 12px;
  font-size: 16px;
}

.help-content p {
  margin-bottom: 8px;
  color: #595959;
}

.help-content ul {
  margin-bottom: 12px;
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 4px;
  color: #595959;
}

.help-content strong {
  color: #262626;
}

/* 生成结果显示样式 */
.result-display {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #24292e;
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 60px;
  max-height: 300px;
  overflow-y: auto;
}

.result-display:empty::before {
  content: '暂无生成结果';
  color: #6a737d;
  font-style: italic;
}

/* 生成按钮加载状态 */
.ant-btn-loading {
  pointer-events: none;
}
</style>