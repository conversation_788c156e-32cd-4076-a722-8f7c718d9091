from fastapi import APIRouter
from .test import router as test
from .dify_mapping import router as dify_mapping
from .knowledge_base import router as knowledge_base
from .knowledge_api import router as knowledge_api
from .redoc import router as doc

router = APIRouter(prefix="/ai/v1")
router.include_router(test)
router.include_router(dify_mapping)
router.include_router(knowledge_base)
router.include_router(knowledge_api)
router.include_router(doc)