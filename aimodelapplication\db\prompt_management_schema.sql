-- 提示词管理数据库表设计
-- 创建时间: 2025-07-30
-- 描述: 用于存储提示词管理相关的配置信息

-- 主表：提示词配置表
CREATE TABLE `prompt_configurations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `knowledge_base_id` varchar(64) NOT NULL COMMENT '知识库ID',
  `business_introduction` text COMMENT '业务介绍',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词配置主表';

-- 名词定义表
CREATE TABLE `prompt_term_definitions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID，关联prompt_configurations.id',
  `term_name` varchar(255) NOT NULL COMMENT '名词名称',
  `term_definition` text NOT NULL COMMENT '名词定义',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_term_name` (`term_name`),
  CONSTRAINT `fk_term_config_id` FOREIGN KEY (`config_id`) REFERENCES `prompt_configurations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='名词定义表';

-- 业务提示表
CREATE TABLE `prompt_business_prompts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID，关联prompt_configurations.id',
  `prompt_content` text NOT NULL COMMENT '业务提示内容',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_business_config_id` FOREIGN KEY (`config_id`) REFERENCES `prompt_configurations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务提示表';

-- 额外依据表
CREATE TABLE `prompt_extra_evidence` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID，关联prompt_configurations.id',
  `evidence_name` varchar(255) NOT NULL COMMENT '依据名称',
  `evidence_description` text NOT NULL COMMENT '依据描述',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_evidence_name` (`evidence_name`),
  CONSTRAINT `fk_evidence_config_id` FOREIGN KEY (`config_id`) REFERENCES `prompt_configurations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='额外依据表';

-- 创建索引优化查询性能
CREATE INDEX `idx_knowledge_base_created` ON `prompt_configurations` (`knowledge_base_id`, `created_at`);
CREATE INDEX `idx_config_sort` ON `prompt_term_definitions` (`config_id`, `sort_order`);
CREATE INDEX `idx_config_sort_business` ON `prompt_business_prompts` (`config_id`, `sort_order`);
CREATE INDEX `idx_config_sort_evidence` ON `prompt_extra_evidence` (`config_id`, `sort_order`);

-- 插入示例数据（可选）
-- INSERT INTO `prompt_configurations` (`knowledge_base_id`, `business_introduction`, `created_by`) 
-- VALUES ('kb_001', '当前系统为堡垒机系统', 'system');

-- 查询示例
-- 查询某个知识库的完整配置
/*
SELECT 
    pc.id,
    pc.knowledge_base_id,
    pc.business_introduction,
    GROUP_CONCAT(DISTINCT CONCAT(ptd.term_name, ':', ptd.term_definition) ORDER BY ptd.sort_order SEPARATOR ';') as term_definitions,
    GROUP_CONCAT(DISTINCT pbp.prompt_content ORDER BY pbp.sort_order SEPARATOR ';') as business_prompts,
    GROUP_CONCAT(DISTINCT CONCAT(pee.evidence_name, ':', pee.evidence_description) ORDER BY pee.sort_order SEPARATOR ';') as extra_evidence
FROM prompt_configurations pc
LEFT JOIN prompt_term_definitions ptd ON pc.id = ptd.config_id AND ptd.is_deleted = 0
LEFT JOIN prompt_business_prompts pbp ON pc.id = pbp.config_id AND pbp.is_deleted = 0
LEFT JOIN prompt_extra_evidence pee ON pc.id = pee.config_id AND pee.is_deleted = 0
WHERE pc.knowledge_base_id = 'kb_001' AND pc.is_deleted = 0
GROUP BY pc.id;
*/
