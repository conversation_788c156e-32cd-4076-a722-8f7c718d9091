# OAuth配置已移至主配置文件

## 🔧 配置变更说明

### 问题描述
启动时出现错误：
```
Could not resolve placeholder 'oauth.auth.client-id' in value "${oauth.auth.client-id}"
```

### 解决方案
将OAuth配置从独立的 `application-ad.yml` 文件移动到主配置文件 `application.yaml` 中。

## 📁 配置文件变更

### ❌ 已删除的文件
- `aiplatformbackend/src/main/resources/application-ad.yml`

### ✅ 更新的文件
- `aiplatformbackend/src/main/resources/application.yaml`

## 📋 当前配置内容

在 `application.yaml` 中添加了以下配置：

```yaml
# TAM零信任身份服务中心OAuth2.0认证配置
oauth:
  auth:
    # TAM认证服务器配置 - 使用实际的服务器地址
    base-url: ${OAUTH_BASE_URL:https://sso.das-security.cn}
    token-url: ${OAUTH_TOKEN_URL:https://sso.das-security.cn/iam/auth/oauth2/accessToken}
    userinfo-url: ${OAUTH_USERINFO_URL:https://sso.das-security.cn/iam/auth/oauth2/userinfo}
    
    # 应用认证信息（已获取的实际值）
    client-id: ${OAUTH_CLIENT_ID:UK59v8}
    client-secret: ${OAUTH_CLIENT_SECRET:M02KuH}
    
    # 回调地址
    redirect-uri: ${OAUTH_REDIRECT_URI:http://localhost:3000/login}
    
    # 请求超时时间（毫秒）
    timeout: 10000
    
    # 用户匹配规则配置
    user-matching:
      # 是否自动创建不存在的用户
      auto-create-user: true
      
      # 默认用户角色
      default-role: user
      
      # 默认产品权限
      default-product: default
      
      # 用户类型标识
      user-type: oauth_user
```

## 🔑 配置说明

### 环境变量支持
配置支持环境变量覆盖，格式为 `${环境变量名:默认值}`：

- `OAUTH_BASE_URL`: OAuth服务器基础地址
- `OAUTH_CLIENT_ID`: 客户端ID
- `OAUTH_CLIENT_SECRET`: 客户端密钥
- `OAUTH_REDIRECT_URI`: 回调地址

### 默认值
如果没有设置环境变量，将使用以下默认值：
- **Client ID**: `UK59v8`
- **Client Secret**: `M02KuH`
- **服务器地址**: `https://sso.das-security.cn`
- **回调地址**: `http://localhost:3000/login`

## 🚀 启动验证

### 1. 清理并重新编译
```bash
cd aiplatformbackend
mvn clean compile
```

### 2. 启动应用
```bash
mvn spring-boot:run
```

### 3. 验证配置加载
启动日志中应该能看到OAuth配置被正确加载，不再出现配置占位符错误。

## 🔧 不同环境的配置方式

### 开发环境
直接使用 `application.yaml` 中的默认配置即可。

### 测试环境
可以通过环境变量覆盖：
```bash
export OAUTH_REDIRECT_URI=http://test.your-domain.com/login
mvn spring-boot:run
```

### 生产环境
建议使用环境变量保护敏感信息：
```bash
export OAUTH_CLIENT_SECRET=your_production_secret
export OAUTH_REDIRECT_URI=https://your-domain.com/login
mvn spring-boot:run
```

## 📋 配置验证清单

- [x] OAuth配置已移至主配置文件
- [x] 删除了独立的配置文件
- [x] 支持环境变量覆盖
- [x] 设置了合理的默认值
- [x] 包含完整的配置项

## 🎯 下一步操作

1. **启动后端服务**
   ```bash
   cd aiplatformbackend
   mvn spring-boot:run
   ```

2. **验证配置生效**
   - 检查启动日志无配置错误
   - 访问 `http://localhost:8083` 确认服务正常

3. **测试OAuth接口**
   ```bash
   curl -X POST http://localhost:8083/api/AuthUser/oauth-login \
     -H "Content-Type: application/json" \
     -d '{"code":"test","state":"test"}'
   ```

4. **前后端联调**
   - 启动前端: `npm run dev`
   - 访问: `http://localhost:3000/login`
   - 测试OAuth认证流程

## 🔐 安全注意事项

### 生产环境建议
1. **使用环境变量**
   ```bash
   export OAUTH_CLIENT_SECRET=actual_secret
   ```

2. **配置HTTPS回调地址**
   ```bash
   export OAUTH_REDIRECT_URI=https://your-domain.com/login
   ```

3. **限制配置文件权限**
   ```bash
   chmod 600 application.yaml
   ```

### 敏感信息保护
- ❌ 不要将生产环境的client_secret提交到版本控制
- ✅ 使用环境变量或密钥管理服务
- ✅ 定期轮换认证密钥

## 📞 故障排除

### 如果仍然出现配置错误
1. 检查YAML格式是否正确（注意缩进）
2. 确认没有多余的配置文件
3. 重启IDE和应用服务
4. 检查环境变量是否正确设置

### 常见问题
- **配置不生效**: 检查YAML缩进和语法
- **环境变量不生效**: 确认变量名和格式正确
- **启动失败**: 查看完整的错误日志

现在配置已经正确设置，应该可以正常启动了！🎉
